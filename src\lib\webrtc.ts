import { WebRTCConfig, SignalingMessage } from "@/types";

export const WEBRTC_CONFIG: WebRTCConfig = {
  iceServers: [
    { urls: "stun:stun.l.google.com:19302" },
    { urls: "stun:stun.l.google.com:5349" },
    { urls: "stun:stun1.l.google.com:3478" },
    { urls: "stun:stun1.l.google.com:5349" },
    { urls: "stun:stun2.l.google.com:19302" },
    { urls: "stun:stun2.l.google.com:5349" },
    { urls: "stun:stun3.l.google.com:3478" },
    { urls: "stun:stun3.l.google.com:5349" },
    { urls: "stun:stun4.l.google.com:19302" },
    { urls: "stun:stun4.l.google.com:5349" }
  ],
  iceCandidatePoolSize: 10,
  bundlePolicy: "max-bundle",
  rtcpMuxPolicy: "require",
};

export class WebRTCManager {
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private onSignalingMessage: ((message: SignalingMessage) => void) | null =
    null;
  private onDataChannelOpen: (() => void) | null = null;
  private onDataChannelMessage: ((data: ArrayBuffer) => void) | null = null;
  private onDataChannelClose: (() => void) | null = null;
  private onConnectionStateChange:
    | ((state: RTCPeerConnectionState) => void)
    | null = null;
  private onError: ((error: string) => void) | null = null;

  // Connection state management
  private connectionState: RTCPeerConnectionState = "new";
  private iceGatheringState: RTCIceGatheringState = "new";
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private isInitiator = false;

  constructor() {
    this.setupPeerConnection();
  }

  private setupPeerConnection() {
    this.peerConnection = new RTCPeerConnection(WEBRTC_CONFIG);

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.onSignalingMessage) {
        this.onSignalingMessage({
          type: "ice-candidate",
          from: "", // Will be set by the caller
          data: event.candidate,
          timestamp: Date.now(),
        });
      }
    };

    // Handle ICE gathering state changes
    this.peerConnection.onicegatheringstatechange = () => {
      if (this.peerConnection) {
        this.iceGatheringState = this.peerConnection.iceGatheringState;
        console.log("ICE gathering state:", this.iceGatheringState);
      }
    };

    // Handle ICE connection state changes
    this.peerConnection.oniceconnectionstatechange = () => {
      if (this.peerConnection) {
        console.log(
          "ICE connection state:",
          this.peerConnection.iceConnectionState
        );

        if (
          this.peerConnection.iceConnectionState === "failed" ||
          this.peerConnection.iceConnectionState === "disconnected"
        ) {
          this.handleConnectionFailure();
        }
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection) {
        this.connectionState = this.peerConnection.connectionState;
        console.log("Connection state:", this.connectionState);

        if (this.onConnectionStateChange) {
          this.onConnectionStateChange(this.connectionState);
        }

        if (this.connectionState === "connected") {
          this.reconnectAttempts = 0; // Reset on successful connection
        } else if (
          this.connectionState === "failed" ||
          this.connectionState === "disconnected"
        ) {
          this.handleConnectionFailure();
        }
      }
    };

    // Handle incoming data channels
    this.peerConnection.ondatachannel = (event) => {
      const channel = event.channel;
      this.setupDataChannel(channel);
    };
  }

  private setupDataChannel(channel: RTCDataChannel) {
    this.dataChannel = channel;

    // Configure binary type for better mobile compatibility
    channel.binaryType = "arraybuffer";

    channel.onopen = () => {
      console.log("Data channel opened, ready state:", channel.readyState);
      if (this.onDataChannelOpen) {
        this.onDataChannelOpen();
      }
    };

    channel.onmessage = (event) => {
      try {
        if (this.onDataChannelMessage) {
          // Ensure we're working with ArrayBuffer
          let data: ArrayBuffer;
          if (event.data instanceof ArrayBuffer) {
            data = event.data;
          } else if (event.data instanceof Blob) {
            // Handle Blob data (some mobile browsers)
            event.data.arrayBuffer().then((buffer) => {
              if (this.onDataChannelMessage) {
                this.onDataChannelMessage(buffer);
              }
            });
            return;
          } else {
            console.warn("Unexpected data type:", typeof event.data);
            return;
          }

          this.onDataChannelMessage(data);
        }
      } catch (error) {
        console.error("Error processing data channel message:", error);
        if (this.onError) {
          this.onError("Failed to process received data");
        }
      }
    };

    channel.onclose = () => {
      console.log("Data channel closed");
      if (this.onDataChannelClose) {
        this.onDataChannelClose();
      }
    };

    channel.onerror = (error) => {
      console.error("Data channel error:", error);
      if (this.onError) {
        this.onError("Data channel error occurred");
      }
    };
  }

  // Create an offer (caller side)
  async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error("Peer connection not initialized");
    }

    this.isInitiator = true;

    // Create data channel for file transfer with mobile-optimized settings
    this.dataChannel = this.peerConnection.createDataChannel("fileTransfer", {
      ordered: true,
      maxRetransmits: 5,
      maxPacketLifeTime: 3000, // 3 seconds
      protocol: "file-transfer-v1",
    });

    this.setupDataChannel(this.dataChannel);

    const offer = await this.peerConnection.createOffer({
      offerToReceiveAudio: false,
      offerToReceiveVideo: false,
    });
    await this.peerConnection.setLocalDescription(offer);
    return offer;
  }

  // Create an answer (receiver side)
  async createAnswer(
    offer: RTCSessionDescriptionInit
  ): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error("Peer connection not initialized");
    }

    await this.peerConnection.setRemoteDescription(offer);
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);
    return answer;
  }

  // Set remote answer (caller side)
  async setRemoteAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error("Peer connection not initialized");
    }

    await this.peerConnection.setRemoteDescription(answer);
  }

  // Add ICE candidate
  async addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error("Peer connection not initialized");
    }

    await this.peerConnection.addIceCandidate(candidate);
  }

  // Send data through the data channel
  sendData(data: ArrayBuffer): boolean {
    if (!this.dataChannel || this.dataChannel.readyState !== "open") {
      return false;
    }

    try {
      this.dataChannel.send(data);
      return true;
    } catch (error) {
      console.error("Error sending data:", error);
      return false;
    }
  }

  // Check if data channel is ready
  isDataChannelReady(): boolean {
    return this.dataChannel?.readyState === "open";
  }

  // Get connection state
  getConnectionState(): RTCPeerConnectionState | null {
    return this.peerConnection?.connectionState || null;
  }

  // Handle connection failures with reconnection logic
  private handleConnectionFailure() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error("Max reconnection attempts reached");
      if (this.onError) {
        this.onError("Connection failed after multiple attempts");
      }
      return;
    }

    this.reconnectAttempts++;
    console.log(
      `Connection failed, attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`
    );

    // Clear existing timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    // Attempt reconnection after a delay
    this.reconnectTimeout = setTimeout(() => {
      this.attemptReconnection();
    }, 2000 * this.reconnectAttempts); // Exponential backoff
  }

  private async attemptReconnection() {
    try {
      if (this.peerConnection) {
        // Restart ICE (note: restartIce() is synchronous)
        this.peerConnection.restartIce();
        console.log("ICE restart initiated");
      }
    } catch (error) {
      console.error("Failed to restart ICE:", error);
      if (this.onError) {
        this.onError("Failed to restart connection");
      }
    }
  }

  // Get detailed connection info for debugging
  getConnectionInfo() {
    if (!this.peerConnection) return null;

    return {
      connectionState: this.peerConnection.connectionState,
      iceConnectionState: this.peerConnection.iceConnectionState,
      iceGatheringState: this.peerConnection.iceGatheringState,
      signalingState: this.peerConnection.signalingState,
      dataChannelState: this.dataChannel?.readyState || "none",
      reconnectAttempts: this.reconnectAttempts,
    };
  }

  // Event handlers
  onSignaling(callback: (message: SignalingMessage) => void) {
    this.onSignalingMessage = callback;
  }

  onDataChannelReady(callback: () => void) {
    this.onDataChannelOpen = callback;
  }

  onDataReceived(callback: (data: ArrayBuffer) => void) {
    this.onDataChannelMessage = callback;
  }

  onDataChannelClosed(callback: () => void) {
    this.onDataChannelClose = callback;
  }

  onConnectionState(callback: (state: RTCPeerConnectionState) => void) {
    this.onConnectionStateChange = callback;
  }

  onErrorOccurred(callback: (error: string) => void) {
    this.onError = callback;
  }

  // Cleanup
  close() {
    // Clear reconnection timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Reset state
    this.connectionState = "new";
    this.iceGatheringState = "new";
    this.reconnectAttempts = 0;
  }
}
