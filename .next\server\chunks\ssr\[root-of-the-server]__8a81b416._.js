module.exports=[54799,(a,b,c)=>{b.exports=a.x("crypto",()=>require("crypto"))},40777,a=>{"use strict";a.s(["default",()=>t],40777);var b=a.i(87924),c=a.i(72131);function d({onFileSelect:a,selectedFile:d}){let e=(0,c.useRef)(null);return(0,b.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg",children:[(0,b.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Select File to Share"}),(0,b.jsx)("input",{ref:e,type:"file",onChange:b=>{let c=b.target.files?.[0];c&&a(c)},className:"hidden",accept:"*/*"}),(0,b.jsx)("div",{onClick:()=>{e.current?.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-colors",children:d?(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsx)("div",{className:"text-green-600 dark:text-green-400",children:(0,b.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"text-lg font-medium text-gray-900 dark:text-white",children:d.name}),(0,b.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[(a=>{if(0===a)return"0 Bytes";let b=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,b)).toFixed(2))+" "+["Bytes","KB","MB","GB"][b]})(d.size)," • ",d.type||"Unknown type"]})]}),(0,b.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:"Click to select a different file"})]}):(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsx)("div",{className:"text-gray-400 dark:text-gray-500",children:(0,b.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Choose a file to share"}),(0,b.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Click here or drag and drop any file"})]})]})}),d&&(0,b.jsx)("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,b.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[(0,b.jsx)("span",{className:"font-medium",children:"Ready to share!"})," Select a device from the list below to send this file."]})})]})}function e({devices:a,onDeviceSelect:c,selectedFile:d}){return(0,b.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Nearby Devices"}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,b.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Scanning..."})]})]}),0===a.length?(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,b.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"})})}),(0,b.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-2",children:"No devices found"}),(0,b.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:"Make sure other devices are on the same Wi-Fi network and have ShareWeb open"})]}):(0,b.jsx)("div",{className:"space-y-3",children:a.map(a=>(0,b.jsxs)("div",{onClick:()=>d&&c(a),className:`flex items-center justify-between p-4 rounded-lg border transition-all ${d?"cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 border-gray-200 dark:border-gray-600":"cursor-not-allowed opacity-50 border-gray-200 dark:border-gray-600"}`,children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:`${a.isOnline?"text-green-600 dark:text-green-400":"text-gray-400 dark:text-gray-500"}`,children:(a=>{let c=a.toLowerCase();return c.includes("iphone")||c.includes("android")?(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"})}):c.includes("ipad")||c.includes("tablet")?(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"})}):(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})(a.name)}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:a.name}),(0,b.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(a=>{let b=Math.floor((Date.now()-a)/1e3),c=Math.floor(b/60),d=Math.floor(c/60);return b<60?"Just now":c<60?`${c}m ago`:d<24?`${d}h ago`:"Offline"})(a.lastSeen)})]})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("div",{className:`w-3 h-3 rounded-full ${a.isOnline?"bg-green-500":"bg-gray-400"}`}),d&&(0,b.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]},a.id))}),!d&&a.length>0&&(0,b.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,b.jsxs)("p",{className:"text-sm text-yellow-800 dark:text-yellow-200",children:[(0,b.jsx)("span",{className:"font-medium",children:"Select a file first"})," to send it to one of these devices."]})})]})}function f({progress:a}){let c=a=>{if(0===a)return"0 Bytes";let b=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,b)).toFixed(2))+" "+["Bytes","KB","MB","GB"][b]};return(0,b.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-200 dark:border-gray-700",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:(a=>{switch(a){case"preparing":case"transferring":return"text-blue-600 dark:text-blue-400";case"completed":return"text-green-600 dark:text-green-400";case"failed":return"text-red-600 dark:text-red-400";default:return"text-gray-600 dark:text-gray-400"}})(a.status),children:(a=>{switch(a){case"preparing":return(0,b.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})});case"transferring":return(0,b.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})});case"completed":return(0,b.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})});case"failed":return(0,b.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"cancelled":return(0,b.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})});default:return null}})(a.status)}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("p",{className:"font-medium text-gray-900 dark:text-white",children:["Transfer ",a.transferId.slice(0,8),"..."]}),(0,b.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 capitalize",children:a.status})]})]}),(0,b.jsxs)("div",{className:"text-right",children:[(0,b.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[a.percentage.toFixed(1),"%"]}),(0,b.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[c(a.bytesTransferred)," / ",c(a.totalBytes)]})]})]}),(0,b.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2",children:(0,b.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${(a=>{switch(a){case"preparing":case"transferring":return"bg-blue-500";case"completed":return"bg-green-500";case"failed":return"bg-red-500";default:return"bg-gray-500"}})(a.status)}`,style:{width:`${Math.min(a.percentage,100)}%`}})}),"transferring"===a.status&&(0,b.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,b.jsx)("span",{children:"Transferring..."}),(0,b.jsx)("span",{children:"P2P Connection"})]}),"completed"===a.status&&(0,b.jsxs)("div",{className:"flex justify-between text-xs text-green-600 dark:text-green-400",children:[(0,b.jsx)("span",{children:"Transfer completed successfully"}),(0,b.jsx)("span",{children:"✓ Verified"})]}),"failed"===a.status&&(0,b.jsxs)("div",{className:"flex justify-between text-xs text-red-600 dark:text-red-400",children:[(0,b.jsx)("span",{children:"Transfer failed"}),(0,b.jsx)("span",{children:"Connection lost"})]})]})}function g({request:a,onAccept:c,onReject:d}){var e;return(0,b.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border-l-4 border-blue-500 animate-pulse-slow",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,b.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(e=a.fileType).startsWith("image/")?(0,b.jsx)("svg",{className:"w-8 h-8 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}):e.startsWith("video/")?(0,b.jsx)("svg",{className:"w-8 h-8 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"})}):e.startsWith("audio/")?(0,b.jsx)("svg",{className:"w-8 h-8 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"})}):e.includes("pdf")?(0,b.jsx)("svg",{className:"w-8 h-8 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})}):(0,b.jsx)("svg",{className:"w-8 h-8 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["Incoming file from ",(0,b.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:a.senderName})]}),(0,b.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:(a=>{let b=Math.floor((Date.now()-a)/1e3),c=Math.floor(b/60);return b<60?"Just now":c<60?`${c}m ago`:"A while ago"})(a.timestamp)})]})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),(0,b.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium",children:"Pending"})]})]}),(0,b.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4",children:(0,b.jsx)("div",{className:"flex items-center justify-between",children:(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-medium text-gray-900 dark:text-white truncate",children:a.fileName}),(0,b.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[(a=>{if(0===a)return"0 Bytes";let b=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,b)).toFixed(2))+" "+["Bytes","KB","MB","GB"][b]})(a.fileSize)," • ",a.fileType||"Unknown type"]})]})})}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Do you want to receive this file?"}),(0,b.jsxs)("div",{className:"flex space-x-3",children:[(0,b.jsx)("button",{onClick:()=>d(a.id),className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors",children:"Decline"}),(0,b.jsxs)("button",{onClick:()=>c(a.id),className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center space-x-2",children:[(0,b.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,b.jsx)("span",{children:"Accept"})]})]})]})]})}function h({id:a,type:d,title:e,message:f,duration:g=5e3,onClose:h}){let[i,j]=(0,c.useState)(!0);(0,c.useEffect)(()=>{let b=setTimeout(()=>{j(!1),setTimeout(()=>h(a),300)},g);return()=>clearTimeout(b)},[a,g,h]);let k=()=>{switch(d){case"success":return"text-green-800 dark:text-green-200";case"error":return"text-red-800 dark:text-red-200";case"warning":return"text-yellow-800 dark:text-yellow-200";default:return"text-blue-800 dark:text-blue-200"}};return(0,b.jsx)("div",{className:`fixed top-4 right-4 max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 z-50 ${i?"translate-x-0 opacity-100":"translate-x-full opacity-0"} ${(()=>{switch(d){case"success":return"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"}})()}`,children:(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(d){case"success":return(0,b.jsx)("svg",{className:"w-5 h-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})});case"error":return(0,b.jsx)("svg",{className:"w-5 h-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"warning":return(0,b.jsx)("svg",{className:"w-5 h-5 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})});default:return(0,b.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}})()}),(0,b.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[(0,b.jsx)("p",{className:`text-sm font-medium ${k()}`,children:e}),(0,b.jsx)("p",{className:`mt-1 text-sm ${k()} opacity-90`,children:f})]}),(0,b.jsx)("div",{className:"ml-4 flex-shrink-0 flex",children:(0,b.jsx)("button",{onClick:()=>{j(!1),setTimeout(()=>h(a),300)},className:`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${k()} hover:opacity-75`,children:(0,b.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})})]})})}function i({notifications:a,onClose:c}){return(0,b.jsx)("div",{className:"fixed top-0 right-0 z-50 p-4 space-y-4",children:a.map((a,d)=>(0,b.jsx)("div",{style:{top:`${80*d}px`},className:"relative",children:(0,b.jsx)(h,{...a,onClose:c})},a.id))})}var j=a.i(54799);let k={randomUUID:j.randomUUID},l=new Uint8Array(256),m=l.length,n=[];for(let a=0;a<256;++a)n.push((a+256).toString(16).slice(1));let o=function(a,b,c){if(k.randomUUID&&!b&&!a)return k.randomUUID();let d=(a=a||{}).random??a.rng?.()??(m>l.length-16&&((0,j.randomFillSync)(l),m=0),l.slice(m,m+=16));if(d.length<16)throw Error("Random bytes length must be >= 16");if(d[6]=15&d[6]|64,d[8]=63&d[8]|128,b){if((c=c||0)<0||c+16>b.length)throw RangeError(`UUID byte range ${c}:${c+15} is out of buffer bounds`);for(let a=0;a<16;++a)b[c+a]=d[a];return b}return function(a,b=0){return(n[a[b+0]]+n[a[b+1]]+n[a[b+2]]+n[a[b+3]]+"-"+n[a[b+4]]+n[a[b+5]]+"-"+n[a[b+6]]+n[a[b+7]]+"-"+n[a[b+8]]+n[a[b+9]]+"-"+n[a[b+10]]+n[a[b+11]]+n[a[b+12]]+n[a[b+13]]+n[a[b+14]]+n[a[b+15]]).toLowerCase()}(d)};class p{deviceId;deviceName;baseUrl;pollingInterval=null;heartbeatInterval=null;onDevicesUpdate=null;onMessage=null;isPolling=!1;constructor(a,b,c="/api/signaling"){this.deviceId=a,this.deviceName=b,this.baseUrl=c}async start(){try{await this.register(),this.startPolling(),this.startHeartbeat(),this.startDeviceDiscovery(),console.log("Signaling client started")}catch(a){throw console.error("Failed to start signaling client:",a),a}}async stop(){this.isPolling=!1,this.pollingInterval&&(clearInterval(this.pollingInterval),this.pollingInterval=null),this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null);try{await fetch(`${this.baseUrl}?deviceId=${this.deviceId}`,{method:"DELETE"})}catch(a){console.error("Failed to unregister device:",a)}console.log("Signaling client stopped")}async register(){if(!(await fetch(this.baseUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",deviceId:this.deviceId,deviceName:this.deviceName})})).ok)throw Error("Failed to register device")}startPolling(){this.isPolling=!0,this.poll()}async poll(){if(this.isPolling){try{let a=await fetch(`${this.baseUrl}?deviceId=${this.deviceId}&action=poll`,{method:"GET"});if(a.ok){let b=await a.json();b.messages&&b.messages.length>0&&b.messages.forEach(a=>{this.onMessage&&this.onMessage(a)})}}catch(a){console.error("Polling error:",a)}this.isPolling&&setTimeout(()=>this.poll(),1e3)}}startHeartbeat(){this.heartbeatInterval=setInterval(async()=>{try{await fetch(this.baseUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"heartbeat",deviceId:this.deviceId})})}catch(a){console.error("Heartbeat error:",a)}},3e4)}startDeviceDiscovery(){let a=async()=>{try{let a=await fetch(`${this.baseUrl}?deviceId=${this.deviceId}&action=devices`,{method:"GET"});if(a.ok){let b=await a.json();this.onDevicesUpdate&&b.devices&&this.onDevicesUpdate(b.devices)}}catch(a){console.error("Device discovery error:",a)}};a(),setInterval(a,5e3)}async sendMessage(a,b){try{if(!(await fetch(this.baseUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"send",deviceId:this.deviceId,targetId:a,message:{...b,from:this.deviceId,timestamp:Date.now()}})})).ok)throw Error("Failed to send message")}catch(a){throw console.error("Failed to send message:",a),a}}onDevices(a){this.onDevicesUpdate=a}onSignalingMessage(a){this.onMessage=a}getDeviceId(){return this.deviceId}getDeviceName(){return this.deviceName}}let q={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun.l.google.com:5349"},{urls:"stun:stun1.l.google.com:3478"},{urls:"stun:stun1.l.google.com:5349"},{urls:"stun:stun2.l.google.com:19302"},{urls:"stun:stun2.l.google.com:5349"},{urls:"stun:stun3.l.google.com:3478"},{urls:"stun:stun3.l.google.com:5349"},{urls:"stun:stun4.l.google.com:19302"},{urls:"stun:stun4.l.google.com:5349"}],iceCandidatePoolSize:10,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"};class r{peerConnection=null;dataChannel=null;onSignalingMessage=null;onDataChannelOpen=null;onDataChannelMessage=null;onDataChannelClose=null;onConnectionStateChange=null;onError=null;connectionState="new";iceGatheringState="new";reconnectAttempts=0;maxReconnectAttempts=3;reconnectTimeout=null;isInitiator=!1;constructor(){this.setupPeerConnection()}setupPeerConnection(){this.peerConnection=new RTCPeerConnection(q),this.peerConnection.onicecandidate=a=>{a.candidate&&this.onSignalingMessage&&this.onSignalingMessage({type:"ice-candidate",from:"",data:a.candidate,timestamp:Date.now()})},this.peerConnection.onicegatheringstatechange=()=>{this.peerConnection&&(this.iceGatheringState=this.peerConnection.iceGatheringState,console.log("ICE gathering state:",this.iceGatheringState))},this.peerConnection.oniceconnectionstatechange=()=>{this.peerConnection&&(console.log("ICE connection state:",this.peerConnection.iceConnectionState),("failed"===this.peerConnection.iceConnectionState||"disconnected"===this.peerConnection.iceConnectionState)&&this.handleConnectionFailure())},this.peerConnection.onconnectionstatechange=()=>{this.peerConnection&&(this.connectionState=this.peerConnection.connectionState,console.log("Connection state:",this.connectionState),this.onConnectionStateChange&&this.onConnectionStateChange(this.connectionState),"connected"===this.connectionState?this.reconnectAttempts=0:("failed"===this.connectionState||"disconnected"===this.connectionState)&&this.handleConnectionFailure())},this.peerConnection.ondatachannel=a=>{let b=a.channel;this.setupDataChannel(b)}}setupDataChannel(a){this.dataChannel=a,a.binaryType="arraybuffer",a.onopen=()=>{console.log("Data channel opened, ready state:",a.readyState),this.onDataChannelOpen&&this.onDataChannelOpen()},a.onmessage=a=>{try{if(this.onDataChannelMessage){let b;if(a.data instanceof ArrayBuffer)b=a.data;else if(a.data instanceof Blob)return void a.data.arrayBuffer().then(a=>{this.onDataChannelMessage&&this.onDataChannelMessage(a)});else return void console.warn("Unexpected data type:",typeof a.data);this.onDataChannelMessage(b)}}catch(a){console.error("Error processing data channel message:",a),this.onError&&this.onError("Failed to process received data")}},a.onclose=()=>{console.log("Data channel closed"),this.onDataChannelClose&&this.onDataChannelClose()},a.onerror=a=>{console.error("Data channel error:",a),this.onError&&this.onError("Data channel error occurred")}}async createOffer(){if(!this.peerConnection)throw Error("Peer connection not initialized");this.isInitiator=!0,this.dataChannel=this.peerConnection.createDataChannel("fileTransfer",{ordered:!0,maxRetransmits:5,maxPacketLifeTime:3e3,protocol:"file-transfer-v1"}),this.setupDataChannel(this.dataChannel);let a=await this.peerConnection.createOffer({offerToReceiveAudio:!1,offerToReceiveVideo:!1});return await this.peerConnection.setLocalDescription(a),a}async createAnswer(a){if(!this.peerConnection)throw Error("Peer connection not initialized");await this.peerConnection.setRemoteDescription(a);let b=await this.peerConnection.createAnswer();return await this.peerConnection.setLocalDescription(b),b}async setRemoteAnswer(a){if(!this.peerConnection)throw Error("Peer connection not initialized");await this.peerConnection.setRemoteDescription(a)}async addIceCandidate(a){if(!this.peerConnection)throw Error("Peer connection not initialized");await this.peerConnection.addIceCandidate(a)}sendData(a){if(!this.dataChannel||"open"!==this.dataChannel.readyState)return!1;try{return this.dataChannel.send(a),!0}catch(a){return console.error("Error sending data:",a),!1}}isDataChannelReady(){return this.dataChannel?.readyState==="open"}getConnectionState(){return this.peerConnection?.connectionState||null}handleConnectionFailure(){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.error("Max reconnection attempts reached"),this.onError&&this.onError("Connection failed after multiple attempts");return}this.reconnectAttempts++,console.log(`Connection failed, attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`),this.reconnectTimeout&&clearTimeout(this.reconnectTimeout),this.reconnectTimeout=setTimeout(()=>{this.attemptReconnection()},2e3*this.reconnectAttempts)}async attemptReconnection(){try{this.peerConnection&&(this.peerConnection.restartIce(),console.log("ICE restart initiated"))}catch(a){console.error("Failed to restart ICE:",a),this.onError&&this.onError("Failed to restart connection")}}getConnectionInfo(){return this.peerConnection?{connectionState:this.peerConnection.connectionState,iceConnectionState:this.peerConnection.iceConnectionState,iceGatheringState:this.peerConnection.iceGatheringState,signalingState:this.peerConnection.signalingState,dataChannelState:this.dataChannel?.readyState||"none",reconnectAttempts:this.reconnectAttempts}:null}onSignaling(a){this.onSignalingMessage=a}onDataChannelReady(a){this.onDataChannelOpen=a}onDataReceived(a){this.onDataChannelMessage=a}onDataChannelClosed(a){this.onDataChannelClose=a}onConnectionState(a){this.onConnectionStateChange=a}onErrorOccurred(a){this.onError=a}close(){this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.dataChannel&&(this.dataChannel.close(),this.dataChannel=null),this.peerConnection&&(this.peerConnection.close(),this.peerConnection=null),this.connectionState="new",this.iceGatheringState="new",this.reconnectAttempts=0}}class s{webrtc;onProgressUpdate=null;onTransferComplete=null;onTransferError=null;sendingFile=null;sendingChunks=[];currentChunkIndex=0;transferId="";receivingMetadata=null;receivedChunks=new Map;expectedChunks=0;constructor(a){this.webrtc=a,this.setupEventHandlers()}setupEventHandlers(){this.webrtc.onDataReceived(a=>{this.handleReceivedData(a)}),this.webrtc.onDataChannelReady(()=>{this.sendingFile&&this.startSending()})}async sendFile(a,b){this.sendingFile=a,this.transferId=b,this.currentChunkIndex=0,this.sendingChunks=await this.createFileChunks(a,b),a.name,a.size,a.type,this.sendingChunks.length,this.updateProgress({transferId:b,bytesTransferred:0,totalBytes:a.size,percentage:0,status:"preparing"}),this.webrtc.isDataChannelReady()&&this.startSending()}async createFileChunks(a,b){let c=[],d=Math.ceil(a.size/65536);for(let e=0;e<d;e++){let f=65536*e,g=Math.min(f+65536,a.size),h=a.slice(f,g),i=await h.arrayBuffer();c.push({id:b,index:e,data:i,isLast:e===d-1,totalChunks:d})}return c}startSending(){if(!this.sendingFile||!this.sendingChunks.length)return;let a={fileName:this.sendingFile.name,fileSize:this.sendingFile.size,fileType:this.sendingFile.type,totalChunks:this.sendingChunks.length,transferId:this.transferId};this.webrtc.sendData(new TextEncoder().encode(JSON.stringify({type:"metadata",data:a})).buffer),this.updateProgress({transferId:this.transferId,bytesTransferred:0,totalBytes:this.sendingFile.size,percentage:0,status:"transferring"}),this.sendNextChunk()}sendNextChunk(){if(this.currentChunkIndex>=this.sendingChunks.length)return void this.updateProgress({transferId:this.transferId,bytesTransferred:this.sendingFile?.size||0,totalBytes:this.sendingFile?.size||0,percentage:100,status:"completed"});let a=this.sendingChunks[this.currentChunkIndex],b={type:"chunk-with-data",header:{id:a.id,index:a.index,isLast:a.isLast,totalChunks:a.totalChunks,dataSize:a.data.byteLength},data:Array.from(new Uint8Array(a.data))};if(!this.webrtc.sendData(new TextEncoder().encode(JSON.stringify(b)).buffer)){console.error("Failed to send chunk",a.index),this.updateProgress({transferId:this.transferId,bytesTransferred:0,totalBytes:this.sendingFile?.size||0,percentage:0,status:"failed"});return}let c=(this.currentChunkIndex+1)*65536,d=Math.min(c/(this.sendingFile?.size||1)*100,100);this.updateProgress({transferId:this.transferId,bytesTransferred:Math.min(c,this.sendingFile?.size||0),totalBytes:this.sendingFile?.size||0,percentage:d,status:"transferring"}),this.currentChunkIndex++,setTimeout(()=>this.sendNextChunk(),50)}handleReceivedData(a){try{let b=new TextDecoder().decode(a),c=JSON.parse(b);"metadata"===c.type?this.handleMetadata(c.data):"chunk-with-data"===c.type?this.handleCombinedChunk(c):"chunk"===c.type&&this.handleChunkHeader(c.data)}catch(b){console.error("Error parsing received data:",b),this.handleChunkData(a)}}handleMetadata(a){this.receivingMetadata=a,this.receivedChunks.clear(),this.expectedChunks=a.totalChunks,this.updateProgress({transferId:a.transferId,bytesTransferred:0,totalBytes:a.fileSize,percentage:0,status:"transferring"})}lastChunkHeader=null;handleChunkHeader(a){this.lastChunkHeader=a}handleChunkData(a){if(!this.lastChunkHeader||!this.receivingMetadata)return;let b=this.lastChunkHeader.index;this.receivedChunks.set(b,a);let c=65536*this.receivedChunks.size,d=Math.min(c/this.receivingMetadata.fileSize*100,100);this.updateProgress({transferId:this.receivingMetadata.transferId,bytesTransferred:Math.min(c,this.receivingMetadata.fileSize),totalBytes:this.receivingMetadata.fileSize,percentage:d,status:"transferring"}),this.receivedChunks.size===this.expectedChunks&&this.assembleFile(),this.lastChunkHeader=null}handleCombinedChunk(a){if(!this.receivingMetadata)return void console.error("Received chunk without metadata");let{header:b,data:c}=a,d=b.index,e=new Uint8Array(c).buffer;if(e.byteLength!==b.dataSize)return void console.error(`Chunk ${d} size mismatch: expected ${b.dataSize}, got ${e.byteLength}`);this.receivedChunks.set(d,e),console.log(`Received chunk ${d}/${b.totalChunks-1}`);let f=65536*this.receivedChunks.size,g=Math.min(f/this.receivingMetadata.fileSize*100,100);this.updateProgress({transferId:this.receivingMetadata.transferId,bytesTransferred:Math.min(f,this.receivingMetadata.fileSize),totalBytes:this.receivingMetadata.fileSize,percentage:g,status:"transferring"}),this.receivedChunks.size===this.expectedChunks&&(console.log("All chunks received, assembling file..."),this.assembleFile())}assembleFile(){if(!this.receivingMetadata)return;let a=[];for(let b=0;b<this.expectedChunks;b++){let c=this.receivedChunks.get(b);c&&a.push(c)}let b=new Blob(a,{type:this.receivingMetadata.fileType});this.updateProgress({transferId:this.receivingMetadata.transferId,bytesTransferred:this.receivingMetadata.fileSize,totalBytes:this.receivingMetadata.fileSize,percentage:100,status:"completed"}),this.onTransferComplete&&this.onTransferComplete(b,this.receivingMetadata),this.downloadFile(b,this.receivingMetadata.fileName),this.receivingMetadata=null,this.receivedChunks.clear()}downloadFile(a,b){let c=URL.createObjectURL(a),d=document.createElement("a");d.href=c,d.download=b,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(c)}updateProgress(a){this.onProgressUpdate&&this.onProgressUpdate(a)}onProgress(a){this.onProgressUpdate=a}onComplete(a){this.onTransferComplete=a}onError(a){this.onTransferError=a}}function t(){let[a,h]=(0,c.useState)(null),[j,k]=(0,c.useState)("");(0,c.useEffect)(()=>{let a=["iPhone","MacBook","iPad","Android","Windows PC","Laptop"];k(`${a[Math.floor(Math.random()*a.length)]} ${Math.floor(100*Math.random())}`)},[]);let{devices:l,transferRequests:m,activeTransfers:n,isConnected:q,deviceId:t,sendFile:u,acceptTransfer:v,rejectTransfer:w}=function(a){let[b,d]=(0,c.useState)([]),[e,f]=(0,c.useState)([]),[g,h]=(0,c.useState)([]),[i,j]=(0,c.useState)(!1),k=(0,c.useRef)(null),l=(0,c.useRef)(new Map),m=(0,c.useRef)(new Map),n=(0,c.useRef)(o());(0,c.useEffect)(()=>((async()=>{try{let b=new p(n.current,a);k.current=b,b.onDevices(a=>{d(a)}),b.onSignalingMessage(q),await b.start(),j(!0)}catch(a){console.error("Failed to initialize signaling:",a),j(!1)}})(),()=>{k.current&&k.current.stop(),l.current.forEach(a=>a.close()),l.current.clear(),m.current.clear()}),[a]);let q=(0,c.useCallback)(async a=>{let{type:b,from:c,data:d}=a;switch(b){case"transfer-request":t(c,d);break;case"transfer-response":u(c,d);break;case"offer":await v(c,d);break;case"answer":await w(c,d);break;case"ice-candidate":await x(c,d)}},[]),t=(a,c)=>{let d=b.find(b=>b.id===a),e={id:c.transferId,senderId:a,senderName:d?.name||"Unknown Device",fileName:c.fileName,fileSize:c.fileSize,fileType:c.fileType,timestamp:Date.now()};f(a=>[...a,e])},u=async(a,b)=>{b.accepted?await y(a,b.transferId):console.log("Transfer rejected by",a)},v=async(a,b)=>{console.log("Handling offer from",a);let c=new r;l.current.set(a,c),c.onConnectionState(b=>{console.log(`Connection state with ${a}:`,b),"connected"===b?j(!0):("failed"===b||"disconnected"===b)&&j(!1)}),c.onErrorOccurred(b=>{console.error(`WebRTC error with ${a}:`,b),l.current.delete(a),m.current.delete(a)});let d=new s(c);m.current.set(a,d),d.onProgress(a=>{h(b=>{let c=b.findIndex(b=>b.transferId===a.transferId);if(!(c>=0))return[...b,a];{let d=[...b];return d[c]=a,d}})}),c.onSignaling(async b=>{k.current&&await k.current.sendMessage(a,b)});try{let d=await c.createAnswer(b);k.current&&await k.current.sendMessage(a,{type:"answer",data:d})}catch(b){console.error("Failed to create answer:",b),l.current.delete(a),m.current.delete(a)}},w=async(a,b)=>{let c=l.current.get(a);c&&await c.setRemoteAnswer(b)},x=async(a,b)=>{let c=l.current.get(a);c&&await c.addIceCandidate(b)},y=async(a,b)=>{console.log("Initiating WebRTC connection to",a);let c=new r;l.current.set(a,c),c.onConnectionState(b=>{console.log(`Connection state with ${a}:`,b),"connected"===b?j(!0):("failed"===b||"disconnected"===b)&&j(!1)}),c.onErrorOccurred(b=>{console.error(`WebRTC error with ${a}:`,b),l.current.delete(a),m.current.delete(a)});let d=new s(c);m.current.set(a,d),d.onProgress(a=>{h(b=>{let c=b.findIndex(b=>b.transferId===a.transferId);if(!(c>=0))return[...b,a];{let d=[...b];return d[c]=a,d}})}),c.onSignaling(async b=>{k.current&&await k.current.sendMessage(a,b)});try{let b=await c.createOffer();k.current&&await k.current.sendMessage(a,{type:"offer",data:b})}catch(b){console.error("Failed to create offer:",b),l.current.delete(a),m.current.delete(a)}},z=async(a,b)=>{let c=o();try{k.current&&await k.current.sendMessage(b.id,{type:"transfer-request",data:{transferId:c,fileName:a.name,fileSize:a.size,fileType:a.type}});let d=m.current.get(b.id);d?await d.sendFile(a,c):console.log("File transfer manager not ready, will send after connection is established")}catch(a){throw console.error("Failed to send file:",a),a}},A=async a=>{let b=e.find(b=>b.id===a);b&&k.current&&(await k.current.sendMessage(b.senderId,{type:"transfer-response",data:{transferId:a,accepted:!0}}),f(b=>b.filter(b=>b.id!==a)))},B=async a=>{let b=e.find(b=>b.id===a);b&&k.current&&(await k.current.sendMessage(b.senderId,{type:"transfer-response",data:{transferId:a,accepted:!1}}),f(b=>b.filter(b=>b.id!==a)))};return{devices:b,transferRequests:e,activeTransfers:g,isConnected:i,deviceId:n.current,sendFile:z,acceptTransfer:A,rejectTransfer:B}}(j),{notifications:x,removeNotification:y,success:z,error:A,info:B}=function(){let[a,b]=(0,c.useState)([]),d=(0,c.useCallback)((a,c,d,e)=>{let f=o(),g={id:f,type:a,title:c,message:d,duration:e};return b(a=>[...a,g]),f},[]),e=(0,c.useCallback)(a=>{b(b=>b.filter(b=>b.id!==a))},[]),f=(0,c.useCallback)((a,b,c)=>d("success",a,b,c),[d]),g=(0,c.useCallback)((a,b,c)=>d("error",a,b,c),[d]),h=(0,c.useCallback)((a,b,c)=>d("info",a,b,c),[d]),i=(0,c.useCallback)((a,b,c)=>d("warning",a,b,c),[d]);return{notifications:a,addNotification:d,removeNotification:e,success:f,error:g,info:h,warning:i,clear:(0,c.useCallback)(()=>{b([])},[])}}(),C=async b=>{if(a)try{await u(a,b),z("Transfer Request Sent",`Sending ${a.name} to ${b.name}. Waiting for acceptance.`),h(null)}catch(c){console.error("Failed to send file:",c),A("Transfer Failed",`Failed to send ${a.name} to ${b.name}. Please try again.`)}},D=async a=>{try{await v(a),z("Transfer Accepted","File transfer has been accepted and will begin shortly.")}catch(a){console.error("Failed to accept transfer:",a),A("Accept Failed","Failed to accept the file transfer. Please try again.")}},E=async a=>{try{await w(a),B("Transfer Rejected","The file transfer request has been declined.")}catch(a){console.error("Failed to reject transfer:",a),A("Reject Failed","Failed to reject the file transfer. Please try again.")}};return(0,b.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:[(0,b.jsx)(i,{notifications:x,onClose:y}),(0,b.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,b.jsxs)("header",{className:"text-center mb-8",children:[(0,b.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-2",children:"ShareWeb"}),(0,b.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Share files instantly with nearby devices"}),(0,b.jsxs)("div",{className:"flex items-center justify-center space-x-4 mt-4",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("div",{className:`w-3 h-3 rounded-full ${q?"bg-green-500":"bg-red-500"}`}),(0,b.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:q?"Connected":"Connecting..."})]}),(0,b.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Device: ",j]}),(0,b.jsxs)("div",{className:"text-xs text-gray-400 dark:text-gray-500",children:["ID: ",t.slice(0,8),"..."]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto",children:[(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)(d,{onFileSelect:a=>{h(a)},selectedFile:a}),(0,b.jsx)(e,{devices:l,onDeviceSelect:C,selectedFile:a})]}),(0,b.jsxs)("div",{className:"space-y-6",children:[m.length>0&&(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Incoming Requests"}),m.map(a=>(0,b.jsx)(g,{request:a,onAccept:D,onReject:E},a.id))]}),n.length>0&&(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Active Transfers"}),n.map(a=>(0,b.jsx)(f,{progress:a},a.transferId))]}),0===m.length&&0===n.length&&(0,b.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-8 text-center",children:[(0,b.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,b.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,b.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No active transfers or requests"})]})]})]})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8a81b416._.js.map