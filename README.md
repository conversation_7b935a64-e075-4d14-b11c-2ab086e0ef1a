# ShareWeb - Browser-Based File Sharing

ShareWeb is a browser-based file sharing application similar to Apple's AirDrop that enables peer-to-peer file transfers over local Wi-Fi/LAN networks without requiring a central server for file storage.

## Features

- 🚀 **Instant File Sharing**: Select file → choose recipient → accept transfer → complete
- 🔒 **Secure P2P Transfer**: Direct browser-to-browser file transfer using WebRTC DataChannels with built-in DTLS encryption
- 🌐 **Zero Infrastructure Costs**: Deployable on Vercel's free tier with no paid dependencies
- 📱 **Cross-Platform**: Works on any device with a modern web browser
- 🔍 **Device Discovery**: Automatic discovery of nearby devices on the same Wi-Fi network
- 📊 **Real-time Progress**: Live transfer progress indicators and status updates
- 🎯 **User Consent**: Explicit accept/reject for all incoming file transfer requests
- 📦 **File Chunking**: Reliable 64KB chunking for large file transfers
- 🔄 **Auto-Download**: Automatic file download upon successful transfer completion

## Technical Architecture

### Frontend (Next.js 15)

- **File Picker Interface**: Drag-and-drop file selection with file type detection
- **Device Discovery UI**: Real-time list of nearby available devices
- **Transfer Management**: Progress indicators, accept/reject dialogs, and notifications
- **Responsive Design**: Works on desktop and mobile devices

### Peer-to-Peer Transfer (WebRTC)

- **Direct Data Channels**: Browser-to-browser file transfer using WebRTC DataChannels
- **File Chunking**: 64KB chunks for reliable streaming of large files
- **Automatic Reassembly**: Seamless file reconstruction on receiver side
- **Built-in Security**: DTLS encryption provided by WebRTC

### Device Discovery & Signaling

- **Lightweight Signaling**: Vercel Edge Functions for peer discovery and connection setup
- **Temporary Registration**: Random device IDs for same-network discovery
- **Presence Broadcasting**: Periodic heartbeats to show available devices
- **Free STUN Servers**: Uses Google's free STUN servers for NAT traversal

## Quick Start

### Local Development

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd share-web
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Open in browser**
   - Navigate to `http://localhost:3000`
   - Open the same URL on multiple devices on the same Wi-Fi network

### Deployment on Vercel

1. **Deploy to Vercel**

   ```bash
   npm install -g vercel
   vercel
   ```

2. **Or use Vercel Dashboard**

   - Connect your GitHub repository to Vercel
   - Deploy automatically on push

3. **Access your deployment**
   - Use the provided Vercel URL on devices connected to the same Wi-Fi network

## Usage Guide

### Sending Files

1. **Select a File**: Click the file picker area or drag and drop a file
2. **Choose Recipient**: Select a device from the "Nearby Devices" list
3. **Wait for Acceptance**: The recipient will see a transfer request
4. **Monitor Progress**: Watch the transfer progress in real-time

### Receiving Files

1. **Keep App Open**: Ensure ShareWeb is open in your browser
2. **Accept Requests**: Click "Accept" when you receive a transfer request
3. **Automatic Download**: Files will automatically download when complete

### Device Discovery

- Devices automatically appear in the "Nearby Devices" list
- Green indicator shows online devices
- Device names are randomly generated (e.g., "iPhone 42", "MacBook 17")
- Devices disappear after 1 minute of inactivity

## Browser Compatibility

ShareWeb requires a modern browser with WebRTC support:

- ✅ Chrome 56+
- ✅ Firefox 51+
- ✅ Safari 11+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Security Features

- **End-to-End Encryption**: WebRTC's built-in DTLS encryption
- **User Consent**: All transfers require explicit acceptance
- **Device Identification**: Clear sender identification before accepting
- **Local Network Only**: Works only on same Wi-Fi/LAN networks
- **No Data Storage**: Files never touch the server - direct P2P transfer

## Infrastructure Costs

ShareWeb is designed to run on Vercel's free tier with zero infrastructure costs:

- **Vercel Free Tier**: 100GB bandwidth, 100 function invocations per day
- **Free STUN Servers**: Uses Google's free STUN servers
- **No Database**: In-memory device registry (resets on deployment)
- **No File Storage**: All files transfer peer-to-peer

## Limitations

- **Same Network Required**: Devices must be on the same Wi-Fi/LAN network
- **Browser Dependent**: Requires modern browser with WebRTC support
- **No TURN Server**: May not work behind restrictive NATs (corporate networks)
- **Memory-Based Registry**: Device list resets on server restart
- **File Size**: Limited by browser memory (typically 1-2GB max)
