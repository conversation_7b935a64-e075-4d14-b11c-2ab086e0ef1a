(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["chunks/turbopack-edge-wrapper_a89aeee8.js",{otherChunks:["chunks/_7f82b0d6._.js","chunks/[root-of-the-server]__69987f34._.js"],runtimeModuleIds:[92380]}]),(()=>{let e;if(!Array.isArray(globalThis.TURBOPACK))return;let t=new WeakMap;function n(e,t){this.m=e,this.e=t}let r=n.prototype,o=Object.prototype.hasOwnProperty,u="undefined"!=typeof Symbol&&Symbol.toStringTag;function i(e,t,n){o.call(e,t)||Object.defineProperty(e,t,n)}function l(e,t){let n=e[t];return n||(n=a(t),e[t]=n),n}function a(e){return{exports:{},error:void 0,id:e,namespaceObject:void 0}}function s(e,t){i(e,"__esModule",{value:!0}),u&&i(e,u,{value:"Module"});let n=0;for(;n<t.length;){let r=t[n++],o=t[n++];"function"==typeof t[n]?i(e,r,{get:o,set:t[n++],enumerable:!0}):i(e,r,{get:o,enumerable:!0})}Object.seal(e)}r.s=function(e,t){let n,r;null!=t?r=(n=l(this.c,t)).exports:(n=this.m,r=this.e),n.namespaceObject=r,s(r,e)},r.j=function(e,n){var r,u;let i,a,s;null!=n?a=(i=l(this.c,n)).exports:(i=this.m,a=this.e);let c=(r=i,u=a,(s=t.get(r))||(t.set(r,s=[]),r.exports=r.namespaceObject=new Proxy(u,{get(e,t){if(o.call(e,t)||"default"===t||"__esModule"===t)return Reflect.get(e,t);for(let e of s){let n=Reflect.get(e,t);if(void 0!==n)return n}},ownKeys(e){let t=Reflect.ownKeys(e);for(let e of s)for(let n of Reflect.ownKeys(e))"default"===n||t.includes(n)||t.push(n);return t}})),s);"object"==typeof e&&null!==e&&c.push(e)},r.v=function(e,t){(null!=t?l(this.c,t):this.m).exports=e},r.n=function(e,t){let n;(n=null!=t?l(this.c,t):this.m).exports=n.namespaceObject=e};let c=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,f=[null,c({}),c([]),c(c)];function d(e,t,n){let r=[],o=-1;for(let t=e;("object"==typeof t||"function"==typeof t)&&!f.includes(t);t=c(t))for(let n of Object.getOwnPropertyNames(t))r.push(n,function(e,t){return()=>e[t]}(e,n)),-1===o&&"default"===n&&(o=r.length-1);return n&&o>=0||(o>=0?r[o]=()=>e:r.push("default",()=>e)),s(t,r),t}function h(e){return"function"==typeof e?function(...t){return e.apply(this,t)}:Object.create(null)}function p(e){return"string"==typeof e?e:e.path}function m(){let e,t;return{promise:new Promise((n,r)=>{t=r,e=n}),resolve:e,reject:t}}r.i=function(e){let t=x(e,this.m);if(t.namespaceObject)return t.namespaceObject;let n=t.exports;return t.namespaceObject=d(n,h(n),n&&n.__esModule)},r.A=function(e){return this.r(e)(this.i.bind(this))},r.t="function"==typeof require?require:function(){throw Error("Unexpected use of runtime require")},r.r=function(e){return x(e,this.m).exports},r.f=function(e){function t(t){if(o.call(e,t))return e[t].module();let n=Error(`Cannot find module '${t}'`);throw n.code="MODULE_NOT_FOUND",n}return t.keys=()=>Object.keys(e),t.resolve=t=>{if(o.call(e,t))return e[t].id();let n=Error(`Cannot find module '${t}'`);throw n.code="MODULE_NOT_FOUND",n},t.import=async e=>await t(e),t};let b=Symbol("turbopack queues"),y=Symbol("turbopack exports"),O=Symbol("turbopack error");function g(e){e&&1!==e.status&&(e.status=1,e.forEach(e=>e.queueCount--),e.forEach(e=>e.queueCount--?e.queueCount++:e()))}r.a=function(e,t){let n=this.m,r=t?Object.assign([],{status:-1}):void 0,o=new Set,{resolve:u,reject:i,promise:l}=m(),a=Object.assign(l,{[y]:n.exports,[b]:e=>{r&&e(r),o.forEach(e),a.catch(()=>{})}}),s={get:()=>a,set(e){e!==a&&(a[y]=e)}};Object.defineProperty(n,"exports",s),Object.defineProperty(n,"namespaceObject",s),e(function(e){let t=e.map(e=>{if(null!==e&&"object"==typeof e){if(b in e)return e;if(null!=e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then){let t=Object.assign([],{status:0}),n={[y]:{},[b]:e=>e(t)};return e.then(e=>{n[y]=e,g(t)},e=>{n[O]=e,g(t)}),n}}return{[y]:e,[b]:()=>{}}}),n=()=>t.map(e=>{if(e[O])throw e[O];return e[y]}),{promise:u,resolve:i}=m(),l=Object.assign(()=>i(n),{queueCount:0});function a(e){e!==r&&!o.has(e)&&(o.add(e),e&&0===e.status&&(l.queueCount++,e.push(l)))}return t.map(e=>e[b](a)),l.queueCount?u:n()},function(e){e?i(a[O]=e):u(a[y]),g(r)}),r&&-1===r.status&&(r.status=0)};let w=function(e){let t=new URL(e,"x:/"),n={};for(let e in t)n[e]=t[e];for(let t in n.href=e,n.pathname=e.replace(/[?#].*/,""),n.origin=n.protocol="",n.toString=n.toJSON=(...t)=>e,n)Object.defineProperty(this,t,{enumerable:!0,configurable:!0,value:n[t]})};function _(e,t){throw Error(`Invariant: ${t(e)}`)}w.prototype=URL.prototype,r.U=w,r.z=function(e){throw Error("dynamic usage of require is not supported")},r.g=globalThis;let j=n.prototype;var C=function(e){return e[e.Runtime=0]="Runtime",e[e.Parent=1]="Parent",e[e.Update=2]="Update",e}(C||{});let k=new Map;r.M=k;let R=new Map,U=new Map;async function P(e,t,n){let r;if("string"==typeof n)return M(e,t,$(n));let o=n.included||[],u=o.map(e=>!!k.has(e)||R.get(e));if(u.length>0&&u.every(e=>e))return void await Promise.all(u);let i=n.moduleChunks||[],l=i.map(e=>U.get(e)).filter(e=>e);if(l.length>0){if(l.length===i.length)return void await Promise.all(l);let n=new Set;for(let e of i)U.has(e)||n.add(e);for(let r of n){let n=M(e,t,$(r));U.set(r,n),l.push(n)}r=Promise.all(l)}else{for(let o of(r=M(e,t,$(n.path)),i))U.has(o)||U.set(o,r)}for(let e of o)R.has(e)||R.set(e,r);await r}j.l=function(e){return P(1,this.m.id,e)};let v=Promise.resolve(void 0),T=new WeakMap;function M(t,n,r){let o=e.loadChunkCached(t,r),u=T.get(o);if(void 0===u){let e=T.set.bind(T,o,v);u=o.then(e).catch(e=>{let o;switch(t){case 0:o=`as a runtime dependency of chunk ${n}`;break;case 1:o=`from module ${n}`;break;case 2:o="from an HMR update";break;default:_(t,e=>`Unknown source type: ${e}`)}throw Error(`Failed to load chunk ${r} ${o}${e?`: ${e}`:""}`,e?{cause:e}:void 0)}),T.set(o,u)}return u}function $(e){return`${e.split("/").map(e=>encodeURIComponent(e)).join("/")}`}j.L=function(e){return M(1,this.m.id,e)},j.R=function(e){let t=this.r(e);return t?.default??t},j.P=function(e){return`/ROOT/${e??""}`},j.b=function(e){let t=new Blob([`self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};
self.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(e.reverse().map($),null,2)};
importScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`],{type:"text/javascript"});return URL.createObjectURL(t)};let A=/\.js(?:\?[^#]*)?(?:#.*)?$/;r.w=function(t,n,r){return e.loadWebAssembly(1,this.m.id,t,n,r)},r.u=function(t,n){return e.loadWebAssemblyModule(1,this.m.id,t,n)};let E={};r.c=E;let x=(e,t)=>{let n=E[e];if(n){if(n.error)throw n.error;return n}return K(e,C.Parent,t.id)};function K(e,t,r){let o=k.get(e);"function"!=typeof o&&function(e,t,n){let r;switch(t){case 0:r=`as a runtime entry of chunk ${n}`;break;case 1:r=`because it was required from module ${n}`;break;case 2:r="because of an HMR update";break;default:_(t,e=>`Unknown source type: ${e}`)}throw Error(`Module ${e} was instantiated ${r}, but the module factory is not available. It might have been deleted in an HMR update.`)}(e,t,r);let u=a(e),i=u.exports;E[e]=u;let l=new n(u,i);try{o(l,u,i)}catch(e){throw u.error=e,e}return u.namespaceObject&&u.exports!==u.namespaceObject&&d(u.exports,u.namespaceObject),u}function S(t){let n,r=function(e){if("string"==typeof e)return e;let t=decodeURIComponent(("undefined"!=typeof TURBOPACK_NEXT_CHUNK_URLS?TURBOPACK_NEXT_CHUNK_URLS.pop():e.getAttribute("src")).replace(/[?#].*$/,""));return t.startsWith("")?t.slice(0):t}(t[0]);return 2===t.length?n=t[1]:(n=void 0,!function(e,t,n,r){let o=1;for(;o<e.length;){let t=e[o],r=o+1;for(;r<e.length&&"function"!=typeof e[r];)r++;if(r===e.length)throw Error("malformed chunk format, expected a factory function");if(!n.has(t)){let u=e[r];for(Object.defineProperty(u,"name",{value:"__TURBOPACK__module__evaluation__"});o<r;o++)t=e[o],n.set(t,u)}o=r+1}}(t,0,k)),e.registerChunk(r,n)}function N(e,t,n=!1){let r;try{r=t()}catch(t){throw Error(`Failed to load external module ${e}: ${t}`)}return!n||r.__esModule?r:d(r,h(r),!0)}r.y=async function(e){let t;try{t=await import(e)}catch(t){throw Error(`Failed to load external module ${e}: ${t}`)}return t&&t.__esModule&&t.default&&"default"in t.default?d(t.default,h(t),!0):t},N.resolve=(e,t)=>require.resolve(e,t),r.x=N,(()=>{e={registerChunk(e,o){t.add(e),function(e){let t=n.get(e);if(null!=t){for(let n of t)n.requiredChunks.delete(e),0===n.requiredChunks.size&&r(n.runtimeModuleIds,n.chunkPath);n.delete(e)}}(e),null!=o&&(0===o.otherChunks.length?r(o.runtimeModuleIds,e):function(e,o,u){let i=new Set,l={runtimeModuleIds:u,chunkPath:e,requiredChunks:i};for(let e of o){let r=p(e);if(t.has(r))continue;i.add(r);let o=n.get(r);null==o&&(o=new Set,n.set(r,o)),o.add(l)}0===l.requiredChunks.size&&r(l.runtimeModuleIds,l.chunkPath)}(e,o.otherChunks.filter(e=>{var t;return t=p(e),A.test(t)}),o.runtimeModuleIds))},loadChunkCached(e,t){throw Error("chunk loading is not supported")},async loadWebAssembly(e,t,n,r,u){let i=await o(n,r);return await WebAssembly.instantiate(i,u)},loadWebAssemblyModule:async(e,t,n,r)=>o(n,r)};let t=new Set,n=new Map;function r(e,t){for(let n of e)!function(e,t){let n=E[t];if(n){if(n.error)throw n.error;return}K(t,C.Runtime,e)}(t,n)}async function o(e,t){let n;try{n=t()}catch(e){}if(!n)throw Error(`dynamically loading WebAssembly is not supported in this runtime as global was not injected for chunk '${e}'`);return n}})();let q=globalThis.TURBOPACK;globalThis.TURBOPACK={push:S},q.forEach(S)})();

//# sourceMappingURL=edge-wrapper_a89aeee8.js.map