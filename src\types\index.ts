export interface Device {
  id: string;
  name: string;
  lastSeen: number;
  isOnline: boolean;
}

export interface FileTransferRequest {
  id: string;
  senderId: string;
  senderName: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  timestamp: number;
}

export interface FileTransferProgress {
  transferId: string;
  bytesTransferred: number;
  totalBytes: number;
  percentage: number;
  status: "preparing" | "transferring" | "completed" | "failed" | "cancelled";
}

export interface SignalingMessage {
  type:
    | "offer"
    | "answer"
    | "ice-candidate"
    | "device-discovery"
    | "transfer-request"
    | "transfer-response";
  from: string;
  to?: string;
  data: any;
  timestamp: number;
}

export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  iceCandidatePoolSize?: number;
  bundlePolicy?: RTCBundlePolicy;
  rtcpMuxPolicy?: RTCRtcpMuxPolicy;
}

export interface FileChunk {
  id: string;
  index: number;
  data: ArrayBuffer;
  isLast: boolean;
  totalChunks: number;
}

export interface TransferSession {
  id: string;
  file: File;
  chunks: FileChunk[];
  progress: FileTransferProgress;
  peerConnection: RTCPeerConnection;
  dataChannel: RTCDataChannel;
}
