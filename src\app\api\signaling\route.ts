import { NextRequest, NextResponse } from 'next/server';

export const runtime = "edge";

// In-memory storage for devices and messages (in production, use Redis or similar)
const devices = new Map<string, {
  id: string;
  name: string;
  lastSeen: number;
  isOnline: boolean;
}>();

const pendingMessages = new Map<string, any[]>();

// Cleanup old devices every 30 seconds
setInterval(() => {
  const now = Date.now();
  const OFFLINE_THRESHOLD = 60000; // 1 minute

  for (const [id, device] of devices.entries()) {
    if (now - device.lastSeen > OFFLINE_THRESHOLD) {
      devices.delete(id);
      pendingMessages.delete(id);
    }
  }
}, 30000);

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const deviceId = searchParams.get('deviceId');
  const action = searchParams.get('action');

  if (!deviceId) {
    return NextResponse.json({ error: 'Device ID required' }, { status: 400 });
  }

  if (action === 'poll') {
    // Long polling for messages
    const messages = pendingMessages.get(deviceId) || [];
    pendingMessages.set(deviceId, []);

    // Update device last seen
    const device = devices.get(deviceId);
    if (device) {
      device.lastSeen = Date.now();
      device.isOnline = true;
    }

    return NextResponse.json({ messages });
  }

  if (action === 'devices') {
    // Return list of online devices (excluding the requesting device)
    const onlineDevices = Array.from(devices.values())
      .filter(device => device.id !== deviceId && device.isOnline)
      .map(device => ({
        id: device.id,
        name: device.name,
        lastSeen: device.lastSeen,
        isOnline: device.isOnline,
      }));

    return NextResponse.json({ devices: onlineDevices });
  }

  return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, deviceId, deviceName, targetId, message } = body;

    if (!deviceId) {
      return NextResponse.json({ error: 'Device ID required' }, { status: 400 });
    }

    if (action === 'register') {
      // Register or update device
      devices.set(deviceId, {
        id: deviceId,
        name: deviceName || `Device ${deviceId.slice(0, 8)}`,
        lastSeen: Date.now(),
        isOnline: true,
      });

      return NextResponse.json({ success: true });
    }

    if (action === 'send') {
      // Send message to target device
      if (!targetId || !message) {
        return NextResponse.json({ error: 'Target ID and message required' }, { status: 400 });
      }

      const targetMessages = pendingMessages.get(targetId) || [];
      targetMessages.push({
        ...message,
        from: deviceId,
        timestamp: Date.now(),
      });
      pendingMessages.set(targetId, targetMessages);

      return NextResponse.json({ success: true });
    }

    if (action === 'heartbeat') {
      // Update device heartbeat
      const device = devices.get(deviceId);
      if (device) {
        device.lastSeen = Date.now();
        device.isOnline = true;
      }

      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Signaling error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const deviceId = searchParams.get('deviceId');

  if (!deviceId) {
    return NextResponse.json({ error: 'Device ID required' }, { status: 400 });
  }

  // Remove device
  devices.delete(deviceId);
  pendingMessages.delete(deviceId);

  return NextResponse.json({ success: true });
}

// Enable CORS for cross-origin requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
