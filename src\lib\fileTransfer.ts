import { FileChunk, FileTransferProgress } from "@/types";
import { WebRTCManager } from "./webrtc";

const CHUNK_SIZE = 64 * 1024; // 64KB chunks

export interface FileTransferMetadata {
  fileName: string;
  fileSize: number;
  fileType: string;
  totalChunks: number;
  transferId: string;
}

export class FileTransferManager {
  private webrtc: WebRTCManager;
  private onProgressUpdate: ((progress: FileTransferProgress) => void) | null =
    null;
  private onTransferComplete:
    | ((file: Blob, metadata: FileTransferMetadata) => void)
    | null = null;
  private onTransferError: ((error: string) => void) | null = null;

  // Sender state
  private sendingFile: File | null = null;
  private sendingChunks: FileChunk[] = [];
  private currentChunkIndex = 0;
  private transferId = "";

  // Receiver state
  private receivingMetadata: FileTransferMetadata | null = null;
  private receivedChunks: Map<number, ArrayBuffer> = new Map();
  private expectedChunks = 0;

  constructor(webrtc: WebRTCManager) {
    this.webrtc = webrtc;
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.webrtc.onDataReceived((data: ArrayBuffer) => {
      this.handleReceivedData(data);
    });

    this.webrtc.onDataChannelReady(() => {
      if (this.sendingFile) {
        this.startSending();
      }
    });
  }

  // Sender methods
  async sendFile(file: File, transferId: string): Promise<void> {
    this.sendingFile = file;
    this.transferId = transferId;
    this.currentChunkIndex = 0;

    // Create chunks
    this.sendingChunks = await this.createFileChunks(file, transferId);

    // Send metadata first
    const metadata: FileTransferMetadata = {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      totalChunks: this.sendingChunks.length,
      transferId,
    };

    this.updateProgress({
      transferId,
      bytesTransferred: 0,
      totalBytes: file.size,
      percentage: 0,
      status: "preparing",
    });

    // If data channel is ready, start sending immediately
    if (this.webrtc.isDataChannelReady()) {
      this.startSending();
    }
  }

  private async createFileChunks(
    file: File,
    transferId: string
  ): Promise<FileChunk[]> {
    const chunks: FileChunk[] = [];
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);

    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const blob = file.slice(start, end);
      const arrayBuffer = await blob.arrayBuffer();

      chunks.push({
        id: transferId,
        index: i,
        data: arrayBuffer,
        isLast: i === totalChunks - 1,
        totalChunks,
      });
    }

    return chunks;
  }

  private startSending() {
    if (!this.sendingFile || !this.sendingChunks.length) return;

    // Send metadata first
    const metadata: FileTransferMetadata = {
      fileName: this.sendingFile.name,
      fileSize: this.sendingFile.size,
      fileType: this.sendingFile.type,
      totalChunks: this.sendingChunks.length,
      transferId: this.transferId,
    };

    const metadataMessage = {
      type: "metadata",
      data: metadata,
    };

    this.webrtc.sendData(
      new TextEncoder().encode(JSON.stringify(metadataMessage)).buffer
    );

    // Start sending chunks
    this.updateProgress({
      transferId: this.transferId,
      bytesTransferred: 0,
      totalBytes: this.sendingFile.size,
      percentage: 0,
      status: "transferring",
    });

    this.sendNextChunk();
  }

  private sendNextChunk() {
    if (this.currentChunkIndex >= this.sendingChunks.length) {
      // Transfer complete
      this.updateProgress({
        transferId: this.transferId,
        bytesTransferred: this.sendingFile?.size || 0,
        totalBytes: this.sendingFile?.size || 0,
        percentage: 100,
        status: "completed",
      });
      return;
    }

    const chunk = this.sendingChunks[this.currentChunkIndex];

    // Create a combined message with header and data to avoid race conditions
    const combinedMessage = {
      type: "chunk-with-data",
      header: {
        id: chunk.id,
        index: chunk.index,
        isLast: chunk.isLast,
        totalChunks: chunk.totalChunks,
        dataSize: chunk.data.byteLength,
      },
      data: Array.from(new Uint8Array(chunk.data)), // Convert to array for JSON serialization
    };

    // Send combined message
    const success = this.webrtc.sendData(
      new TextEncoder().encode(JSON.stringify(combinedMessage)).buffer
    );

    if (!success) {
      console.error("Failed to send chunk", chunk.index);
      this.updateProgress({
        transferId: this.transferId,
        bytesTransferred: 0,
        totalBytes: this.sendingFile?.size || 0,
        percentage: 0,
        status: "failed",
      });
      return;
    }

    // Update progress
    const bytesTransferred = (this.currentChunkIndex + 1) * CHUNK_SIZE;
    const percentage = Math.min(
      (bytesTransferred / (this.sendingFile?.size || 1)) * 100,
      100
    );

    this.updateProgress({
      transferId: this.transferId,
      bytesTransferred: Math.min(bytesTransferred, this.sendingFile?.size || 0),
      totalBytes: this.sendingFile?.size || 0,
      percentage,
      status: "transferring",
    });

    this.currentChunkIndex++;

    // Send next chunk with a small delay to avoid overwhelming the channel
    setTimeout(() => this.sendNextChunk(), 50); // Increased delay for mobile compatibility
  }

  // Receiver methods
  private handleReceivedData(data: ArrayBuffer) {
    try {
      // Try to parse as JSON (metadata or chunk with data)
      const text = new TextDecoder().decode(data);
      const message = JSON.parse(text);

      if (message.type === "metadata") {
        this.handleMetadata(message.data);
      } else if (message.type === "chunk-with-data") {
        this.handleCombinedChunk(message);
      } else if (message.type === "chunk") {
        // Legacy support for old format
        this.handleChunkHeader(message.data);
      }
    } catch (error) {
      console.error("Error parsing received data:", error);
      // This might be legacy binary chunk data
      this.handleChunkData(data);
    }
  }

  private handleMetadata(metadata: FileTransferMetadata) {
    this.receivingMetadata = metadata;
    this.receivedChunks.clear();
    this.expectedChunks = metadata.totalChunks;

    this.updateProgress({
      transferId: metadata.transferId,
      bytesTransferred: 0,
      totalBytes: metadata.fileSize,
      percentage: 0,
      status: "transferring",
    });
  }

  private lastChunkHeader: {
    id: string;
    index: number;
    isLast: boolean;
    totalChunks: number;
  } | null = null;

  private handleChunkHeader(chunkInfo: {
    id: string;
    index: number;
    isLast: boolean;
    totalChunks: number;
  }) {
    this.lastChunkHeader = chunkInfo;
  }

  private handleChunkData(data: ArrayBuffer) {
    if (!this.lastChunkHeader || !this.receivingMetadata) return;

    const chunkIndex = this.lastChunkHeader.index;
    this.receivedChunks.set(chunkIndex, data);

    // Update progress
    const bytesTransferred = this.receivedChunks.size * CHUNK_SIZE;
    const percentage = Math.min(
      (bytesTransferred / this.receivingMetadata.fileSize) * 100,
      100
    );

    this.updateProgress({
      transferId: this.receivingMetadata.transferId,
      bytesTransferred: Math.min(
        bytesTransferred,
        this.receivingMetadata.fileSize
      ),
      totalBytes: this.receivingMetadata.fileSize,
      percentage,
      status: "transferring",
    });

    // Check if transfer is complete
    if (this.receivedChunks.size === this.expectedChunks) {
      this.assembleFile();
    }

    this.lastChunkHeader = null;
  }

  private handleCombinedChunk(message: {
    header: {
      id: string;
      index: number;
      isLast: boolean;
      totalChunks: number;
      dataSize: number;
    };
    data: number[];
  }) {
    if (!this.receivingMetadata) {
      console.error("Received chunk without metadata");
      return;
    }

    const { header, data } = message;
    const chunkIndex = header.index;

    // Convert array back to ArrayBuffer
    const chunkData = new Uint8Array(data).buffer;

    // Validate chunk data size
    if (chunkData.byteLength !== header.dataSize) {
      console.error(
        `Chunk ${chunkIndex} size mismatch: expected ${header.dataSize}, got ${chunkData.byteLength}`
      );
      return;
    }

    this.receivedChunks.set(chunkIndex, chunkData);

    console.log(`Received chunk ${chunkIndex}/${header.totalChunks - 1}`);

    // Update progress
    const bytesTransferred = this.receivedChunks.size * CHUNK_SIZE;
    const percentage = Math.min(
      (bytesTransferred / this.receivingMetadata.fileSize) * 100,
      100
    );

    this.updateProgress({
      transferId: this.receivingMetadata.transferId,
      bytesTransferred: Math.min(
        bytesTransferred,
        this.receivingMetadata.fileSize
      ),
      totalBytes: this.receivingMetadata.fileSize,
      percentage,
      status: "transferring",
    });

    // Check if transfer is complete
    if (this.receivedChunks.size === this.expectedChunks) {
      console.log("All chunks received, assembling file...");
      this.assembleFile();
    }
  }

  private assembleFile() {
    if (!this.receivingMetadata) return;

    const chunks: ArrayBuffer[] = [];
    for (let i = 0; i < this.expectedChunks; i++) {
      const chunk = this.receivedChunks.get(i);
      if (chunk) {
        chunks.push(chunk);
      }
    }

    const blob = new Blob(chunks, { type: this.receivingMetadata.fileType });

    this.updateProgress({
      transferId: this.receivingMetadata.transferId,
      bytesTransferred: this.receivingMetadata.fileSize,
      totalBytes: this.receivingMetadata.fileSize,
      percentage: 100,
      status: "completed",
    });

    if (this.onTransferComplete) {
      this.onTransferComplete(blob, this.receivingMetadata);
    }

    // Auto-download the file
    this.downloadFile(blob, this.receivingMetadata.fileName);

    // Reset state
    this.receivingMetadata = null;
    this.receivedChunks.clear();
  }

  private downloadFile(blob: Blob, fileName: string) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  private updateProgress(progress: FileTransferProgress) {
    if (this.onProgressUpdate) {
      this.onProgressUpdate(progress);
    }
  }

  // Event handlers
  onProgress(callback: (progress: FileTransferProgress) => void) {
    this.onProgressUpdate = callback;
  }

  onComplete(callback: (file: Blob, metadata: FileTransferMetadata) => void) {
    this.onTransferComplete = callback;
  }

  onError(callback: (error: string) => void) {
    this.onTransferError = callback;
  }
}
