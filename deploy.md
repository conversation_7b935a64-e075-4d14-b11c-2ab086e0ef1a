# ShareWeb Deployment Guide

## Quick Deploy to Vercel

### Option 1: One-Click Deploy
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/share-web)

### Option 2: Manual Deploy

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel
   ```

4. **Follow the prompts:**
   - Set up and deploy? `Y`
   - Which scope? Select your account
   - Link to existing project? `N`
   - What's your project's name? `share-web` (or your preferred name)
   - In which directory is your code located? `./`

5. **Production deployment**
   ```bash
   vercel --prod
   ```

### Option 3: GitHub Integration

1. **Push to GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/your-username/share-web.git
   git push -u origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Deploy automatically

## Environment Configuration

No environment variables are required for basic functionality. The application uses:

- **Free STUN servers**: `stun:stun.l.google.com:19302`
- **In-memory storage**: Device registry (resets on deployment)
- **Edge Functions**: Automatic scaling on Vercel

## Post-Deployment Testing

1. **Access your deployment**
   - Use the Vercel URL provided after deployment
   - Example: `https://share-web-abc123.vercel.app`

2. **Test on multiple devices**
   - Open the URL on 2+ devices on the same Wi-Fi network
   - Devices should appear in each other's "Nearby Devices" list

3. **Test file transfer**
   - Select a small file (< 1MB) on one device
   - Send to another device
   - Accept the transfer request
   - Verify file downloads automatically

## Troubleshooting

### Devices Not Appearing
- Ensure all devices are on the same Wi-Fi network
- Check browser console for WebRTC errors
- Try refreshing the page
- Verify the signaling API is working: `/api/signaling?deviceId=test&action=devices`

### Transfer Failures
- Check browser WebRTC support
- Verify STUN server connectivity
- Try smaller files first
- Check network firewall settings

### Build Issues
- Run `npm run build -- --no-lint` if linting takes too long
- Ensure all TypeScript types are correct
- Check for missing dependencies

## Performance Optimization

### For Production
- Files are automatically chunked into 64KB pieces
- WebRTC provides direct P2P transfer (no server bandwidth usage)
- Signaling uses minimal server resources
- Device registry is memory-based (no database costs)

### Scaling Considerations
- Vercel free tier: 100GB bandwidth, 100 function invocations/day
- Each device discovery uses 1 function invocation every 5 seconds
- File transfers use no server bandwidth (P2P only)
- Approximately 1,728 device-hours per day on free tier

## Custom Domain (Optional)

1. **Add domain in Vercel dashboard**
   - Go to your project settings
   - Add your custom domain
   - Configure DNS records as instructed

2. **SSL Certificate**
   - Automatically provided by Vercel
   - Required for WebRTC functionality

## Monitoring

### Built-in Analytics
- Vercel provides basic analytics
- Monitor function invocations
- Track bandwidth usage

### Custom Monitoring
- Add console.log statements for debugging
- Use browser developer tools for WebRTC diagnostics
- Monitor signaling API responses

## Security Notes

- All file transfers are encrypted via WebRTC DTLS
- No files are stored on servers
- Device IDs are randomly generated
- Works only on local networks (same Wi-Fi)
- No user authentication required

## Support

For issues:
1. Check browser console for errors
2. Verify WebRTC support: `webrtc.org/testing`
3. Test signaling API directly
4. Open GitHub issue with details

## License

MIT License - Free for personal and commercial use
