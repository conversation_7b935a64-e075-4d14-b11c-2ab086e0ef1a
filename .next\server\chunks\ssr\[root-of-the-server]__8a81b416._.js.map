{"version": 3, "sources": ["turbopack:///[project]/src/app/page.tsx", "turbopack:///[project]/src/components/FilePicker.tsx", "turbopack:///[project]/src/components/DeviceList.tsx", "turbopack:///[project]/src/components/TransferProgress.tsx", "turbopack:///[project]/src/components/TransferRequest.tsx", "turbopack:///[project]/src/components/Notification.tsx", "turbopack:///[project]/node_modules/uuid/dist/esm/native.js", "turbopack:///[project]/node_modules/uuid/dist/esm/rng.js", "turbopack:///[project]/node_modules/uuid/dist/esm/regex.js", "turbopack:///[project]/node_modules/uuid/dist/esm/validate.js", "turbopack:///[project]/node_modules/uuid/dist/esm/stringify.js", "turbopack:///[project]/node_modules/uuid/dist/esm/v4.js", "turbopack:///[project]/src/lib/signaling.ts", "turbopack:///[project]/src/lib/webrtc.ts", "turbopack:///[project]/src/lib/fileTransfer.ts", "turbopack:///[project]/src/hooks/useShareWeb.ts", "turbopack:///[project]/src/hooks/useNotifications.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport FilePicker from \"@/components/FilePicker\";\nimport DeviceList from \"@/components/DeviceList\";\nimport TransferProgress from \"@/components/TransferProgress\";\nimport TransferRequest from \"@/components/TransferRequest\";\nimport { NotificationContainer } from \"@/components/Notification\";\nimport { useShareWeb } from \"@/hooks/useShareWeb\";\nimport { useNotifications } from \"@/hooks/useNotifications\";\nimport { Device } from \"@/types\";\n\nexport default function Home() {\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [deviceName, setDeviceName] = useState<string>(\"\");\n\n  useEffect(() => {\n    // Generate a random device name on first load\n    const names = [\n      \"iPhone\",\n      \"MacBook\",\n      \"iPad\",\n      \"Android\",\n      \"Windows PC\",\n      \"Laptop\",\n    ];\n    const randomName = `${\n      names[Math.floor(Math.random() * names.length)]\n    } ${Math.floor(Math.random() * 100)}`;\n    setDeviceName(randomName);\n  }, []);\n\n  const {\n    devices,\n    transferRequests,\n    activeTransfers,\n    isConnected,\n    deviceId,\n    sendFile,\n    acceptTransfer,\n    rejectTransfer,\n  } = useShareWeb(deviceName);\n\n  const {\n    notifications,\n    removeNotification,\n    success,\n    error: showError,\n    info,\n  } = useNotifications();\n\n  const handleFileSelect = (file: File) => {\n    setSelectedFile(file);\n  };\n\n  const handleDeviceSelect = async (device: Device) => {\n    if (selectedFile) {\n      try {\n        await sendFile(selectedFile, device);\n        success(\n          \"Transfer Request Sent\",\n          `Sending ${selectedFile.name} to ${device.name}. Waiting for acceptance.`\n        );\n        setSelectedFile(null); // Clear selection after sending\n      } catch (err) {\n        console.error(\"Failed to send file:\", err);\n        showError(\n          \"Transfer Failed\",\n          `Failed to send ${selectedFile.name} to ${device.name}. Please try again.`\n        );\n      }\n    }\n  };\n\n  const handleTransferAccept = async (requestId: string) => {\n    try {\n      await acceptTransfer(requestId);\n      success(\n        \"Transfer Accepted\",\n        \"File transfer has been accepted and will begin shortly.\"\n      );\n    } catch (err) {\n      console.error(\"Failed to accept transfer:\", err);\n      showError(\n        \"Accept Failed\",\n        \"Failed to accept the file transfer. Please try again.\"\n      );\n    }\n  };\n\n  const handleTransferReject = async (requestId: string) => {\n    try {\n      await rejectTransfer(requestId);\n      info(\"Transfer Rejected\", \"The file transfer request has been declined.\");\n    } catch (err) {\n      console.error(\"Failed to reject transfer:\", err);\n      showError(\n        \"Reject Failed\",\n        \"Failed to reject the file transfer. Please try again.\"\n      );\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <NotificationContainer\n        notifications={notifications}\n        onClose={removeNotification}\n      />\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-2\">\n            ShareWeb\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300\">\n            Share files instantly with nearby devices\n          </p>\n          <div className=\"flex items-center justify-center space-x-4 mt-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div\n                className={`w-3 h-3 rounded-full ${\n                  isConnected ? \"bg-green-500\" : \"bg-red-500\"\n                }`}\n              ></div>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {isConnected ? \"Connected\" : \"Connecting...\"}\n              </span>\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Device: {deviceName}\n            </div>\n            <div className=\"text-xs text-gray-400 dark:text-gray-500\">\n              ID: {deviceId.slice(0, 8)}...\n            </div>\n          </div>\n        </header>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto\">\n          {/* Left Column - File Selection and Device Discovery */}\n          <div className=\"space-y-6\">\n            <FilePicker\n              onFileSelect={handleFileSelect}\n              selectedFile={selectedFile}\n            />\n            <DeviceList\n              devices={devices}\n              onDeviceSelect={handleDeviceSelect}\n              selectedFile={selectedFile}\n            />\n          </div>\n\n          {/* Right Column - Transfer Status */}\n          <div className=\"space-y-6\">\n            {transferRequests.length > 0 && (\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  Incoming Requests\n                </h3>\n                {transferRequests.map((request) => (\n                  <TransferRequest\n                    key={request.id}\n                    request={request}\n                    onAccept={handleTransferAccept}\n                    onReject={handleTransferReject}\n                  />\n                ))}\n              </div>\n            )}\n\n            {activeTransfers.length > 0 && (\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  Active Transfers\n                </h3>\n                {activeTransfers.map((transfer) => (\n                  <TransferProgress\n                    key={transfer.transferId}\n                    progress={transfer}\n                  />\n                ))}\n              </div>\n            )}\n\n            {transferRequests.length === 0 && activeTransfers.length === 0 && (\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg p-8 text-center\">\n                <div className=\"text-gray-400 dark:text-gray-500 mb-4\">\n                  <svg\n                    className=\"w-16 h-16 mx-auto\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={1}\n                      d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                    />\n                  </svg>\n                </div>\n                <p className=\"text-gray-500 dark:text-gray-400\">\n                  No active transfers or requests\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import { useRef } from 'react';\n\ninterface FilePickerProps {\n  onFileSelect: (file: File) => void;\n  selectedFile: File | null;\n}\n\nexport default function FilePicker({ onFileSelect, selectedFile }: FilePickerProps) {\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      onFileSelect(file);\n    }\n  };\n\n  const handleClick = () => {\n    fileInputRef.current?.click();\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg\">\n      <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n        Select File to Share\n      </h2>\n      \n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        onChange={handleFileChange}\n        className=\"hidden\"\n        accept=\"*/*\"\n      />\n      \n      <div\n        onClick={handleClick}\n        className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-colors\"\n      >\n        {selectedFile ? (\n          <div className=\"space-y-3\">\n            <div className=\"text-green-600 dark:text-green-400\">\n              <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {selectedFile.name}\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {formatFileSize(selectedFile.size)} • {selectedFile.type || 'Unknown type'}\n              </p>\n            </div>\n            <p className=\"text-sm text-blue-600 dark:text-blue-400\">\n              Click to select a different file\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            <div className=\"text-gray-400 dark:text-gray-500\">\n              <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n              </svg>\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                Choose a file to share\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                Click here or drag and drop any file\n              </p>\n            </div>\n          </div>\n        )}\n      </div>\n      \n      {selectedFile && (\n        <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n          <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n            <span className=\"font-medium\">Ready to share!</span> Select a device from the list below to send this file.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n", "import { Device } from '@/types';\n\ninterface DeviceListProps {\n  devices: Device[];\n  onDeviceSelect: (device: Device) => void;\n  selectedFile: File | null;\n}\n\nexport default function DeviceList({ devices, onDeviceSelect, selectedFile }: DeviceListProps) {\n  const getDeviceIcon = (deviceName: string) => {\n    const name = deviceName.toLowerCase();\n    if (name.includes('iphone') || name.includes('android')) {\n      return (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z\" />\n        </svg>\n      );\n    } else if (name.includes('ipad') || name.includes('tablet')) {\n      return (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2\" />\n        </svg>\n      );\n    } else {\n      return (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      );\n    }\n  };\n\n  const getTimeSinceLastSeen = (lastSeen: number) => {\n    const now = Date.now();\n    const diff = now - lastSeen;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n\n    if (seconds < 60) return 'Just now';\n    if (minutes < 60) return `${minutes}m ago`;\n    if (hours < 24) return `${hours}h ago`;\n    return 'Offline';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          Nearby Devices\n        </h2>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n          <span className=\"text-sm text-gray-500 dark:text-gray-400\">Scanning...</span>\n        </div>\n      </div>\n\n      {devices.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 dark:text-gray-500 mb-4\">\n            <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0\" />\n            </svg>\n          </div>\n          <p className=\"text-gray-500 dark:text-gray-400 mb-2\">\n            No devices found\n          </p>\n          <p className=\"text-sm text-gray-400 dark:text-gray-500\">\n            Make sure other devices are on the same Wi-Fi network and have ShareWeb open\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {devices.map((device) => (\n            <div\n              key={device.id}\n              onClick={() => selectedFile && onDeviceSelect(device)}\n              className={`flex items-center justify-between p-4 rounded-lg border transition-all ${\n                selectedFile\n                  ? 'cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 border-gray-200 dark:border-gray-600'\n                  : 'cursor-not-allowed opacity-50 border-gray-200 dark:border-gray-600'\n              }`}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className={`${device.isOnline ? 'text-green-600 dark:text-green-400' : 'text-gray-400 dark:text-gray-500'}`}>\n                  {getDeviceIcon(device.name)}\n                </div>\n                <div>\n                  <p className=\"font-medium text-gray-900 dark:text-white\">\n                    {device.name}\n                  </p>\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {getTimeSinceLastSeen(device.lastSeen)}\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  device.isOnline ? 'bg-green-500' : 'bg-gray-400'\n                }`}></div>\n                {selectedFile && (\n                  <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {!selectedFile && devices.length > 0 && (\n        <div className=\"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\n          <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n            <span className=\"font-medium\">Select a file first</span> to send it to one of these devices.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n", "import { FileTransferProgress } from '@/types';\n\ninterface TransferProgressProps {\n  progress: FileTransferProgress;\n}\n\nexport default function TransferProgress({ progress }: TransferProgressProps) {\n  const formatBytes = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'preparing':\n        return 'text-blue-600 dark:text-blue-400';\n      case 'transferring':\n        return 'text-blue-600 dark:text-blue-400';\n      case 'completed':\n        return 'text-green-600 dark:text-green-400';\n      case 'failed':\n        return 'text-red-600 dark:text-red-400';\n      case 'cancelled':\n        return 'text-gray-600 dark:text-gray-400';\n      default:\n        return 'text-gray-600 dark:text-gray-400';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'preparing':\n        return (\n          <svg className=\"w-5 h-5 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n          </svg>\n        );\n      case 'transferring':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n          </svg>\n        );\n      case 'completed':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      case 'failed':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      case 'cancelled':\n        return (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  const getProgressBarColor = (status: string) => {\n    switch (status) {\n      case 'preparing':\n      case 'transferring':\n        return 'bg-blue-500';\n      case 'completed':\n        return 'bg-green-500';\n      case 'failed':\n        return 'bg-red-500';\n      case 'cancelled':\n        return 'bg-gray-500';\n      default:\n        return 'bg-gray-500';\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-200 dark:border-gray-700\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-3\">\n          <div className={getStatusColor(progress.status)}>\n            {getStatusIcon(progress.status)}\n          </div>\n          <div>\n            <p className=\"font-medium text-gray-900 dark:text-white\">\n              Transfer {progress.transferId.slice(0, 8)}...\n            </p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 capitalize\">\n              {progress.status}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"text-right\">\n          <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n            {progress.percentage.toFixed(1)}%\n          </p>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n            {formatBytes(progress.bytesTransferred)} / {formatBytes(progress.totalBytes)}\n          </p>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2\">\n        <div\n          className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(progress.status)}`}\n          style={{ width: `${Math.min(progress.percentage, 100)}%` }}\n        ></div>\n      </div>\n\n      {/* Transfer Speed and ETA (only show during active transfer) */}\n      {progress.status === 'transferring' && (\n        <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n          <span>Transferring...</span>\n          <span>P2P Connection</span>\n        </div>\n      )}\n\n      {progress.status === 'completed' && (\n        <div className=\"flex justify-between text-xs text-green-600 dark:text-green-400\">\n          <span>Transfer completed successfully</span>\n          <span>✓ Verified</span>\n        </div>\n      )}\n\n      {progress.status === 'failed' && (\n        <div className=\"flex justify-between text-xs text-red-600 dark:text-red-400\">\n          <span>Transfer failed</span>\n          <span>Connection lost</span>\n        </div>\n      )}\n    </div>\n  );\n}\n", "import { FileTransferRequest } from '@/types';\n\ninterface TransferRequestProps {\n  request: FileTransferRequest;\n  onAccept: (requestId: string) => void;\n  onReject: (requestId: string) => void;\n}\n\nexport default function TransferRequest({ request, onAccept, onReject }: TransferRequestProps) {\n  const formatBytes = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const getFileIcon = (fileType: string) => {\n    if (fileType.startsWith('image/')) {\n      return (\n        <svg className=\"w-8 h-8 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n      );\n    } else if (fileType.startsWith('video/')) {\n      return (\n        <svg className=\"w-8 h-8 text-purple-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n        </svg>\n      );\n    } else if (fileType.startsWith('audio/')) {\n      return (\n        <svg className=\"w-8 h-8 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3\" />\n        </svg>\n      );\n    } else if (fileType.includes('pdf')) {\n      return (\n        <svg className=\"w-8 h-8 text-red-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n        </svg>\n      );\n    } else {\n      return (\n        <svg className=\"w-8 h-8 text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n      );\n    }\n  };\n\n  const timeAgo = (timestamp: number) => {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n\n    if (seconds < 60) return 'Just now';\n    if (minutes < 60) return `${minutes}m ago`;\n    return 'A while ago';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border-l-4 border-blue-500 animate-pulse-slow\">\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex items-start space-x-3\">\n          <div className=\"flex-shrink-0\">\n            {getFileIcon(request.fileType)}\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n              Incoming file from <span className=\"text-blue-600 dark:text-blue-400\">{request.senderName}</span>\n            </p>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              {timeAgo(request.timestamp)}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n          <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n            Pending\n          </span>\n        </div>\n      </div>\n\n      <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <p className=\"font-medium text-gray-900 dark:text-white truncate\">\n              {request.fileName}\n            </p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {formatBytes(request.fileSize)} • {request.fileType || 'Unknown type'}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between\">\n        <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n          Do you want to receive this file?\n        </p>\n        \n        <div className=\"flex space-x-3\">\n          <button\n            onClick={() => onReject(request.id)}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors\"\n          >\n            Decline\n          </button>\n          <button\n            onClick={() => onAccept(request.id)}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center space-x-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span>Accept</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import { useEffect, useState } from \"react\";\n\nexport interface NotificationProps {\n  id: string;\n  type: \"success\" | \"error\" | \"info\" | \"warning\";\n  title: string;\n  message: string;\n  duration?: number;\n  onClose: (id: string) => void;\n}\n\nexport default function Notification({\n  id,\n  type,\n  title,\n  message,\n  duration = 5000,\n  onClose,\n}: NotificationProps) {\n  const [isVisible, setIsVisible] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      setTimeout(() => onClose(id), 300); // Wait for animation to complete\n    }, duration);\n\n    return () => clearTimeout(timer);\n  }, [id, duration, onClose]);\n\n  const getIcon = () => {\n    switch (type) {\n      case \"success\":\n        return (\n          <svg\n            className=\"w-5 h-5 text-green-400\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            />\n          </svg>\n        );\n      case \"error\":\n        return (\n          <svg\n            className=\"w-5 h-5 text-red-400\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            />\n          </svg>\n        );\n      case \"warning\":\n        return (\n          <svg\n            className=\"w-5 h-5 text-yellow-400\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            />\n          </svg>\n        );\n      case \"info\":\n      default:\n        return (\n          <svg\n            className=\"w-5 h-5 text-blue-400\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            />\n          </svg>\n        );\n    }\n  };\n\n  const getBackgroundColor = () => {\n    switch (type) {\n      case \"success\":\n        return \"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800\";\n      case \"error\":\n        return \"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800\";\n      case \"warning\":\n        return \"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800\";\n      case \"info\":\n      default:\n        return \"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800\";\n    }\n  };\n\n  const getTextColor = () => {\n    switch (type) {\n      case \"success\":\n        return \"text-green-800 dark:text-green-200\";\n      case \"error\":\n        return \"text-red-800 dark:text-red-200\";\n      case \"warning\":\n        return \"text-yellow-800 dark:text-yellow-200\";\n      case \"info\":\n      default:\n        return \"text-blue-800 dark:text-blue-200\";\n    }\n  };\n\n  return (\n    <div\n      className={`fixed top-4 right-4 max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 z-50 ${\n        isVisible ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"\n      } ${getBackgroundColor()}`}\n    >\n      <div className=\"flex items-start\">\n        <div className=\"flex-shrink-0\">{getIcon()}</div>\n        <div className=\"ml-3 w-0 flex-1\">\n          <p className={`text-sm font-medium ${getTextColor()}`}>{title}</p>\n          <p className={`mt-1 text-sm ${getTextColor()} opacity-90`}>\n            {message}\n          </p>\n        </div>\n        <div className=\"ml-4 flex-shrink-0 flex\">\n          <button\n            onClick={() => {\n              setIsVisible(false);\n              setTimeout(() => onClose(id), 300);\n            }}\n            className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${getTextColor()} hover:opacity-75`}\n          >\n            <svg\n              className=\"w-4 h-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M6 18L18 6M6 6l12 12\"\n              />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Notification Container Component\ninterface NotificationData {\n  id: string;\n  type: \"success\" | \"error\" | \"info\" | \"warning\";\n  title: string;\n  message: string;\n  duration?: number;\n}\n\ninterface NotificationContainerProps {\n  notifications: NotificationData[];\n  onClose: (id: string) => void;\n}\n\nexport function NotificationContainer({\n  notifications,\n  onClose,\n}: NotificationContainerProps) {\n  return (\n    <div className=\"fixed top-0 right-0 z-50 p-4 space-y-4\">\n      {notifications.map((notification, index) => (\n        <div\n          key={notification.id}\n          style={{ top: `${index * 80}px` }}\n          className=\"relative\"\n        >\n          <Notification {...notification} onClose={onClose} />\n        </div>\n      ))}\n    </div>\n  );\n}\n", "import { randomUUID } from 'crypto';\nexport default { randomUUID };\n", "import { randomFillSync } from 'crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n", "import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n", "import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n", "import { Device, SignalingMessage } from '@/types';\n\nexport class SignalingClient {\n  private deviceId: string;\n  private deviceName: string;\n  private baseUrl: string;\n  private pollingInterval: NodeJS.Timeout | null = null;\n  private heartbeatInterval: NodeJS.Timeout | null = null;\n  private onDevicesUpdate: ((devices: Device[]) => void) | null = null;\n  private onMessage: ((message: SignalingMessage) => void) | null = null;\n  private isPolling = false;\n\n  constructor(deviceId: string, deviceName: string, baseUrl = '/api/signaling') {\n    this.deviceId = deviceId;\n    this.deviceName = deviceName;\n    this.baseUrl = baseUrl;\n  }\n\n  async start(): Promise<void> {\n    try {\n      // Register device\n      await this.register();\n      \n      // Start polling for messages\n      this.startPolling();\n      \n      // Start heartbeat\n      this.startHeartbeat();\n      \n      // Start device discovery\n      this.startDeviceDiscovery();\n      \n      console.log('Signaling client started');\n    } catch (error) {\n      console.error('Failed to start signaling client:', error);\n      throw error;\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.isPolling = false;\n    \n    if (this.pollingInterval) {\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n    }\n    \n    if (this.heartbeatInterval) {\n      clearInterval(this.heartbeatInterval);\n      this.heartbeatInterval = null;\n    }\n\n    try {\n      // Unregister device\n      await fetch(`${this.baseUrl}?deviceId=${this.deviceId}`, {\n        method: 'DELETE',\n      });\n    } catch (error) {\n      console.error('Failed to unregister device:', error);\n    }\n\n    console.log('Signaling client stopped');\n  }\n\n  private async register(): Promise<void> {\n    const response = await fetch(this.baseUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        action: 'register',\n        deviceId: this.deviceId,\n        deviceName: this.deviceName,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error('Failed to register device');\n    }\n  }\n\n  private startPolling(): void {\n    this.isPolling = true;\n    this.poll();\n  }\n\n  private async poll(): Promise<void> {\n    if (!this.isPolling) return;\n\n    try {\n      const response = await fetch(\n        `${this.baseUrl}?deviceId=${this.deviceId}&action=poll`,\n        {\n          method: 'GET',\n        }\n      );\n\n      if (response.ok) {\n        const data = await response.json();\n        \n        if (data.messages && data.messages.length > 0) {\n          data.messages.forEach((message: SignalingMessage) => {\n            if (this.onMessage) {\n              this.onMessage(message);\n            }\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Polling error:', error);\n    }\n\n    // Continue polling with a short delay\n    if (this.isPolling) {\n      setTimeout(() => this.poll(), 1000);\n    }\n  }\n\n  private startHeartbeat(): void {\n    this.heartbeatInterval = setInterval(async () => {\n      try {\n        await fetch(this.baseUrl, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            action: 'heartbeat',\n            deviceId: this.deviceId,\n          }),\n        });\n      } catch (error) {\n        console.error('Heartbeat error:', error);\n      }\n    }, 30000); // Send heartbeat every 30 seconds\n  }\n\n  private startDeviceDiscovery(): void {\n    const discoverDevices = async () => {\n      try {\n        const response = await fetch(\n          `${this.baseUrl}?deviceId=${this.deviceId}&action=devices`,\n          {\n            method: 'GET',\n          }\n        );\n\n        if (response.ok) {\n          const data = await response.json();\n          if (this.onDevicesUpdate && data.devices) {\n            this.onDevicesUpdate(data.devices);\n          }\n        }\n      } catch (error) {\n        console.error('Device discovery error:', error);\n      }\n    };\n\n    // Discover devices immediately and then every 5 seconds\n    discoverDevices();\n    setInterval(discoverDevices, 5000);\n  }\n\n  async sendMessage(targetId: string, message: Omit<SignalingMessage, 'from' | 'timestamp'>): Promise<void> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'send',\n          deviceId: this.deviceId,\n          targetId,\n          message: {\n            ...message,\n            from: this.deviceId,\n            timestamp: Date.now(),\n          },\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      throw error;\n    }\n  }\n\n  // Event handlers\n  onDevices(callback: (devices: Device[]) => void): void {\n    this.onDevicesUpdate = callback;\n  }\n\n  onSignalingMessage(callback: (message: SignalingMessage) => void): void {\n    this.onMessage = callback;\n  }\n\n  getDeviceId(): string {\n    return this.deviceId;\n  }\n\n  getDeviceName(): string {\n    return this.deviceName;\n  }\n}\n", "import { WebRTCConfig, SignalingMessage } from \"@/types\";\n\nexport const WEBRTC_CONFIG: WebRTCConfig = {\n  iceServers: [\n    { urls: \"stun:stun.l.google.com:19302\" },\n    { urls: \"stun:stun.l.google.com:5349\" },\n    { urls: \"stun:stun1.l.google.com:3478\" },\n    { urls: \"stun:stun1.l.google.com:5349\" },\n    { urls: \"stun:stun2.l.google.com:19302\" },\n    { urls: \"stun:stun2.l.google.com:5349\" },\n    { urls: \"stun:stun3.l.google.com:3478\" },\n    { urls: \"stun:stun3.l.google.com:5349\" },\n    { urls: \"stun:stun4.l.google.com:19302\" },\n    { urls: \"stun:stun4.l.google.com:5349\" }\n  ],\n  iceCandidatePoolSize: 10,\n  bundlePolicy: \"max-bundle\",\n  rtcpMuxPolicy: \"require\",\n};\n\nexport class WebRTCManager {\n  private peerConnection: RTCPeerConnection | null = null;\n  private dataChannel: RTCDataChannel | null = null;\n  private onSignalingMessage: ((message: SignalingMessage) => void) | null =\n    null;\n  private onDataChannelOpen: (() => void) | null = null;\n  private onDataChannelMessage: ((data: ArrayBuffer) => void) | null = null;\n  private onDataChannelClose: (() => void) | null = null;\n  private onConnectionStateChange:\n    | ((state: RTCPeerConnectionState) => void)\n    | null = null;\n  private onError: ((error: string) => void) | null = null;\n\n  // Connection state management\n  private connectionState: RTCPeerConnectionState = \"new\";\n  private iceGatheringState: RTCIceGatheringState = \"new\";\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 3;\n  private reconnectTimeout: NodeJS.Timeout | null = null;\n  private isInitiator = false;\n\n  constructor() {\n    this.setupPeerConnection();\n  }\n\n  private setupPeerConnection() {\n    this.peerConnection = new RTCPeerConnection(WEBRTC_CONFIG);\n\n    // Handle ICE candidates\n    this.peerConnection.onicecandidate = (event) => {\n      if (event.candidate && this.onSignalingMessage) {\n        this.onSignalingMessage({\n          type: \"ice-candidate\",\n          from: \"\", // Will be set by the caller\n          data: event.candidate,\n          timestamp: Date.now(),\n        });\n      }\n    };\n\n    // Handle ICE gathering state changes\n    this.peerConnection.onicegatheringstatechange = () => {\n      if (this.peerConnection) {\n        this.iceGatheringState = this.peerConnection.iceGatheringState;\n        console.log(\"ICE gathering state:\", this.iceGatheringState);\n      }\n    };\n\n    // Handle ICE connection state changes\n    this.peerConnection.oniceconnectionstatechange = () => {\n      if (this.peerConnection) {\n        console.log(\n          \"ICE connection state:\",\n          this.peerConnection.iceConnectionState\n        );\n\n        if (\n          this.peerConnection.iceConnectionState === \"failed\" ||\n          this.peerConnection.iceConnectionState === \"disconnected\"\n        ) {\n          this.handleConnectionFailure();\n        }\n      }\n    };\n\n    // Handle connection state changes\n    this.peerConnection.onconnectionstatechange = () => {\n      if (this.peerConnection) {\n        this.connectionState = this.peerConnection.connectionState;\n        console.log(\"Connection state:\", this.connectionState);\n\n        if (this.onConnectionStateChange) {\n          this.onConnectionStateChange(this.connectionState);\n        }\n\n        if (this.connectionState === \"connected\") {\n          this.reconnectAttempts = 0; // Reset on successful connection\n        } else if (\n          this.connectionState === \"failed\" ||\n          this.connectionState === \"disconnected\"\n        ) {\n          this.handleConnectionFailure();\n        }\n      }\n    };\n\n    // Handle incoming data channels\n    this.peerConnection.ondatachannel = (event) => {\n      const channel = event.channel;\n      this.setupDataChannel(channel);\n    };\n  }\n\n  private setupDataChannel(channel: RTCDataChannel) {\n    this.dataChannel = channel;\n\n    // Configure binary type for better mobile compatibility\n    channel.binaryType = \"arraybuffer\";\n\n    channel.onopen = () => {\n      console.log(\"Data channel opened, ready state:\", channel.readyState);\n      if (this.onDataChannelOpen) {\n        this.onDataChannelOpen();\n      }\n    };\n\n    channel.onmessage = (event) => {\n      try {\n        if (this.onDataChannelMessage) {\n          // Ensure we're working with ArrayBuffer\n          let data: ArrayBuffer;\n          if (event.data instanceof ArrayBuffer) {\n            data = event.data;\n          } else if (event.data instanceof Blob) {\n            // Handle Blob data (some mobile browsers)\n            event.data.arrayBuffer().then((buffer) => {\n              if (this.onDataChannelMessage) {\n                this.onDataChannelMessage(buffer);\n              }\n            });\n            return;\n          } else {\n            console.warn(\"Unexpected data type:\", typeof event.data);\n            return;\n          }\n\n          this.onDataChannelMessage(data);\n        }\n      } catch (error) {\n        console.error(\"Error processing data channel message:\", error);\n        if (this.onError) {\n          this.onError(\"Failed to process received data\");\n        }\n      }\n    };\n\n    channel.onclose = () => {\n      console.log(\"Data channel closed\");\n      if (this.onDataChannelClose) {\n        this.onDataChannelClose();\n      }\n    };\n\n    channel.onerror = (error) => {\n      console.error(\"Data channel error:\", error);\n      if (this.onError) {\n        this.onError(\"Data channel error occurred\");\n      }\n    };\n  }\n\n  // Create an offer (caller side)\n  async createOffer(): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error(\"Peer connection not initialized\");\n    }\n\n    this.isInitiator = true;\n\n    // Create data channel for file transfer with mobile-optimized settings\n    this.dataChannel = this.peerConnection.createDataChannel(\"fileTransfer\", {\n      ordered: true,\n      maxRetransmits: 5,\n      maxPacketLifeTime: 3000, // 3 seconds\n      protocol: \"file-transfer-v1\",\n    });\n\n    this.setupDataChannel(this.dataChannel);\n\n    const offer = await this.peerConnection.createOffer({\n      offerToReceiveAudio: false,\n      offerToReceiveVideo: false,\n    });\n    await this.peerConnection.setLocalDescription(offer);\n    return offer;\n  }\n\n  // Create an answer (receiver side)\n  async createAnswer(\n    offer: RTCSessionDescriptionInit\n  ): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error(\"Peer connection not initialized\");\n    }\n\n    await this.peerConnection.setRemoteDescription(offer);\n    const answer = await this.peerConnection.createAnswer();\n    await this.peerConnection.setLocalDescription(answer);\n    return answer;\n  }\n\n  // Set remote answer (caller side)\n  async setRemoteAnswer(answer: RTCSessionDescriptionInit): Promise<void> {\n    if (!this.peerConnection) {\n      throw new Error(\"Peer connection not initialized\");\n    }\n\n    await this.peerConnection.setRemoteDescription(answer);\n  }\n\n  // Add ICE candidate\n  async addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {\n    if (!this.peerConnection) {\n      throw new Error(\"Peer connection not initialized\");\n    }\n\n    await this.peerConnection.addIceCandidate(candidate);\n  }\n\n  // Send data through the data channel\n  sendData(data: ArrayBuffer): boolean {\n    if (!this.dataChannel || this.dataChannel.readyState !== \"open\") {\n      return false;\n    }\n\n    try {\n      this.dataChannel.send(data);\n      return true;\n    } catch (error) {\n      console.error(\"Error sending data:\", error);\n      return false;\n    }\n  }\n\n  // Check if data channel is ready\n  isDataChannelReady(): boolean {\n    return this.dataChannel?.readyState === \"open\";\n  }\n\n  // Get connection state\n  getConnectionState(): RTCPeerConnectionState | null {\n    return this.peerConnection?.connectionState || null;\n  }\n\n  // Handle connection failures with reconnection logic\n  private handleConnectionFailure() {\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      console.error(\"Max reconnection attempts reached\");\n      if (this.onError) {\n        this.onError(\"Connection failed after multiple attempts\");\n      }\n      return;\n    }\n\n    this.reconnectAttempts++;\n    console.log(\n      `Connection failed, attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`\n    );\n\n    // Clear existing timeout\n    if (this.reconnectTimeout) {\n      clearTimeout(this.reconnectTimeout);\n    }\n\n    // Attempt reconnection after a delay\n    this.reconnectTimeout = setTimeout(() => {\n      this.attemptReconnection();\n    }, 2000 * this.reconnectAttempts); // Exponential backoff\n  }\n\n  private async attemptReconnection() {\n    try {\n      if (this.peerConnection) {\n        // Restart ICE (note: restartIce() is synchronous)\n        this.peerConnection.restartIce();\n        console.log(\"ICE restart initiated\");\n      }\n    } catch (error) {\n      console.error(\"Failed to restart ICE:\", error);\n      if (this.onError) {\n        this.onError(\"Failed to restart connection\");\n      }\n    }\n  }\n\n  // Get detailed connection info for debugging\n  getConnectionInfo() {\n    if (!this.peerConnection) return null;\n\n    return {\n      connectionState: this.peerConnection.connectionState,\n      iceConnectionState: this.peerConnection.iceConnectionState,\n      iceGatheringState: this.peerConnection.iceGatheringState,\n      signalingState: this.peerConnection.signalingState,\n      dataChannelState: this.dataChannel?.readyState || \"none\",\n      reconnectAttempts: this.reconnectAttempts,\n    };\n  }\n\n  // Event handlers\n  onSignaling(callback: (message: SignalingMessage) => void) {\n    this.onSignalingMessage = callback;\n  }\n\n  onDataChannelReady(callback: () => void) {\n    this.onDataChannelOpen = callback;\n  }\n\n  onDataReceived(callback: (data: ArrayBuffer) => void) {\n    this.onDataChannelMessage = callback;\n  }\n\n  onDataChannelClosed(callback: () => void) {\n    this.onDataChannelClose = callback;\n  }\n\n  onConnectionState(callback: (state: RTCPeerConnectionState) => void) {\n    this.onConnectionStateChange = callback;\n  }\n\n  onErrorOccurred(callback: (error: string) => void) {\n    this.onError = callback;\n  }\n\n  // Cleanup\n  close() {\n    // Clear reconnection timeout\n    if (this.reconnectTimeout) {\n      clearTimeout(this.reconnectTimeout);\n      this.reconnectTimeout = null;\n    }\n\n    if (this.dataChannel) {\n      this.dataChannel.close();\n      this.dataChannel = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    // Reset state\n    this.connectionState = \"new\";\n    this.iceGatheringState = \"new\";\n    this.reconnectAttempts = 0;\n  }\n}\n", "import { FileChunk, FileTransferProgress } from \"@/types\";\nimport { WebRTCManager } from \"./webrtc\";\n\nconst CHUNK_SIZE = 64 * 1024; // 64KB chunks\n\nexport interface FileTransferMetadata {\n  fileName: string;\n  fileSize: number;\n  fileType: string;\n  totalChunks: number;\n  transferId: string;\n}\n\nexport class FileTransferManager {\n  private webrtc: WebRTCManager;\n  private onProgressUpdate: ((progress: FileTransferProgress) => void) | null =\n    null;\n  private onTransferComplete:\n    | ((file: Blob, metadata: FileTransferMetadata) => void)\n    | null = null;\n  private onTransferError: ((error: string) => void) | null = null;\n\n  // Sender state\n  private sendingFile: File | null = null;\n  private sendingChunks: FileChunk[] = [];\n  private currentChunkIndex = 0;\n  private transferId = \"\";\n\n  // Receiver state\n  private receivingMetadata: FileTransferMetadata | null = null;\n  private receivedChunks: Map<number, ArrayBuffer> = new Map();\n  private expectedChunks = 0;\n\n  constructor(webrtc: WebRTCManager) {\n    this.webrtc = webrtc;\n    this.setupEventHandlers();\n  }\n\n  private setupEventHandlers() {\n    this.webrtc.onDataReceived((data: ArrayBuffer) => {\n      this.handleReceivedData(data);\n    });\n\n    this.webrtc.onDataChannelReady(() => {\n      if (this.sendingFile) {\n        this.startSending();\n      }\n    });\n  }\n\n  // Sender methods\n  async sendFile(file: File, transferId: string): Promise<void> {\n    this.sendingFile = file;\n    this.transferId = transferId;\n    this.currentChunkIndex = 0;\n\n    // Create chunks\n    this.sendingChunks = await this.createFileChunks(file, transferId);\n\n    // Send metadata first\n    const metadata: FileTransferMetadata = {\n      fileName: file.name,\n      fileSize: file.size,\n      fileType: file.type,\n      totalChunks: this.sendingChunks.length,\n      transferId,\n    };\n\n    this.updateProgress({\n      transferId,\n      bytesTransferred: 0,\n      totalBytes: file.size,\n      percentage: 0,\n      status: \"preparing\",\n    });\n\n    // If data channel is ready, start sending immediately\n    if (this.webrtc.isDataChannelReady()) {\n      this.startSending();\n    }\n  }\n\n  private async createFileChunks(\n    file: File,\n    transferId: string\n  ): Promise<FileChunk[]> {\n    const chunks: FileChunk[] = [];\n    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);\n\n    for (let i = 0; i < totalChunks; i++) {\n      const start = i * CHUNK_SIZE;\n      const end = Math.min(start + CHUNK_SIZE, file.size);\n      const blob = file.slice(start, end);\n      const arrayBuffer = await blob.arrayBuffer();\n\n      chunks.push({\n        id: transferId,\n        index: i,\n        data: arrayBuffer,\n        isLast: i === totalChunks - 1,\n        totalChunks,\n      });\n    }\n\n    return chunks;\n  }\n\n  private startSending() {\n    if (!this.sendingFile || !this.sendingChunks.length) return;\n\n    // Send metadata first\n    const metadata: FileTransferMetadata = {\n      fileName: this.sendingFile.name,\n      fileSize: this.sendingFile.size,\n      fileType: this.sendingFile.type,\n      totalChunks: this.sendingChunks.length,\n      transferId: this.transferId,\n    };\n\n    const metadataMessage = {\n      type: \"metadata\",\n      data: metadata,\n    };\n\n    this.webrtc.sendData(\n      new TextEncoder().encode(JSON.stringify(metadataMessage)).buffer\n    );\n\n    // Start sending chunks\n    this.updateProgress({\n      transferId: this.transferId,\n      bytesTransferred: 0,\n      totalBytes: this.sendingFile.size,\n      percentage: 0,\n      status: \"transferring\",\n    });\n\n    this.sendNextChunk();\n  }\n\n  private sendNextChunk() {\n    if (this.currentChunkIndex >= this.sendingChunks.length) {\n      // Transfer complete\n      this.updateProgress({\n        transferId: this.transferId,\n        bytesTransferred: this.sendingFile?.size || 0,\n        totalBytes: this.sendingFile?.size || 0,\n        percentage: 100,\n        status: \"completed\",\n      });\n      return;\n    }\n\n    const chunk = this.sendingChunks[this.currentChunkIndex];\n\n    // Create a combined message with header and data to avoid race conditions\n    const combinedMessage = {\n      type: \"chunk-with-data\",\n      header: {\n        id: chunk.id,\n        index: chunk.index,\n        isLast: chunk.isLast,\n        totalChunks: chunk.totalChunks,\n        dataSize: chunk.data.byteLength,\n      },\n      data: Array.from(new Uint8Array(chunk.data)), // Convert to array for JSON serialization\n    };\n\n    // Send combined message\n    const success = this.webrtc.sendData(\n      new TextEncoder().encode(JSON.stringify(combinedMessage)).buffer\n    );\n\n    if (!success) {\n      console.error(\"Failed to send chunk\", chunk.index);\n      this.updateProgress({\n        transferId: this.transferId,\n        bytesTransferred: 0,\n        totalBytes: this.sendingFile?.size || 0,\n        percentage: 0,\n        status: \"failed\",\n      });\n      return;\n    }\n\n    // Update progress\n    const bytesTransferred = (this.currentChunkIndex + 1) * CHUNK_SIZE;\n    const percentage = Math.min(\n      (bytesTransferred / (this.sendingFile?.size || 1)) * 100,\n      100\n    );\n\n    this.updateProgress({\n      transferId: this.transferId,\n      bytesTransferred: Math.min(bytesTransferred, this.sendingFile?.size || 0),\n      totalBytes: this.sendingFile?.size || 0,\n      percentage,\n      status: \"transferring\",\n    });\n\n    this.currentChunkIndex++;\n\n    // Send next chunk with a small delay to avoid overwhelming the channel\n    setTimeout(() => this.sendNextChunk(), 50); // Increased delay for mobile compatibility\n  }\n\n  // Receiver methods\n  private handleReceivedData(data: ArrayBuffer) {\n    try {\n      // Try to parse as JSON (metadata or chunk with data)\n      const text = new TextDecoder().decode(data);\n      const message = JSON.parse(text);\n\n      if (message.type === \"metadata\") {\n        this.handleMetadata(message.data);\n      } else if (message.type === \"chunk-with-data\") {\n        this.handleCombinedChunk(message);\n      } else if (message.type === \"chunk\") {\n        // Legacy support for old format\n        this.handleChunkHeader(message.data);\n      }\n    } catch (error) {\n      console.error(\"Error parsing received data:\", error);\n      // This might be legacy binary chunk data\n      this.handleChunkData(data);\n    }\n  }\n\n  private handleMetadata(metadata: FileTransferMetadata) {\n    this.receivingMetadata = metadata;\n    this.receivedChunks.clear();\n    this.expectedChunks = metadata.totalChunks;\n\n    this.updateProgress({\n      transferId: metadata.transferId,\n      bytesTransferred: 0,\n      totalBytes: metadata.fileSize,\n      percentage: 0,\n      status: \"transferring\",\n    });\n  }\n\n  private lastChunkHeader: {\n    id: string;\n    index: number;\n    isLast: boolean;\n    totalChunks: number;\n  } | null = null;\n\n  private handleChunkHeader(chunkInfo: {\n    id: string;\n    index: number;\n    isLast: boolean;\n    totalChunks: number;\n  }) {\n    this.lastChunkHeader = chunkInfo;\n  }\n\n  private handleChunkData(data: ArrayBuffer) {\n    if (!this.lastChunkHeader || !this.receivingMetadata) return;\n\n    const chunkIndex = this.lastChunkHeader.index;\n    this.receivedChunks.set(chunkIndex, data);\n\n    // Update progress\n    const bytesTransferred = this.receivedChunks.size * CHUNK_SIZE;\n    const percentage = Math.min(\n      (bytesTransferred / this.receivingMetadata.fileSize) * 100,\n      100\n    );\n\n    this.updateProgress({\n      transferId: this.receivingMetadata.transferId,\n      bytesTransferred: Math.min(\n        bytesTransferred,\n        this.receivingMetadata.fileSize\n      ),\n      totalBytes: this.receivingMetadata.fileSize,\n      percentage,\n      status: \"transferring\",\n    });\n\n    // Check if transfer is complete\n    if (this.receivedChunks.size === this.expectedChunks) {\n      this.assembleFile();\n    }\n\n    this.lastChunkHeader = null;\n  }\n\n  private handleCombinedChunk(message: {\n    header: {\n      id: string;\n      index: number;\n      isLast: boolean;\n      totalChunks: number;\n      dataSize: number;\n    };\n    data: number[];\n  }) {\n    if (!this.receivingMetadata) {\n      console.error(\"Received chunk without metadata\");\n      return;\n    }\n\n    const { header, data } = message;\n    const chunkIndex = header.index;\n\n    // Convert array back to ArrayBuffer\n    const chunkData = new Uint8Array(data).buffer;\n\n    // Validate chunk data size\n    if (chunkData.byteLength !== header.dataSize) {\n      console.error(\n        `Chunk ${chunkIndex} size mismatch: expected ${header.dataSize}, got ${chunkData.byteLength}`\n      );\n      return;\n    }\n\n    this.receivedChunks.set(chunkIndex, chunkData);\n\n    console.log(`Received chunk ${chunkIndex}/${header.totalChunks - 1}`);\n\n    // Update progress\n    const bytesTransferred = this.receivedChunks.size * CHUNK_SIZE;\n    const percentage = Math.min(\n      (bytesTransferred / this.receivingMetadata.fileSize) * 100,\n      100\n    );\n\n    this.updateProgress({\n      transferId: this.receivingMetadata.transferId,\n      bytesTransferred: Math.min(\n        bytesTransferred,\n        this.receivingMetadata.fileSize\n      ),\n      totalBytes: this.receivingMetadata.fileSize,\n      percentage,\n      status: \"transferring\",\n    });\n\n    // Check if transfer is complete\n    if (this.receivedChunks.size === this.expectedChunks) {\n      console.log(\"All chunks received, assembling file...\");\n      this.assembleFile();\n    }\n  }\n\n  private assembleFile() {\n    if (!this.receivingMetadata) return;\n\n    const chunks: ArrayBuffer[] = [];\n    for (let i = 0; i < this.expectedChunks; i++) {\n      const chunk = this.receivedChunks.get(i);\n      if (chunk) {\n        chunks.push(chunk);\n      }\n    }\n\n    const blob = new Blob(chunks, { type: this.receivingMetadata.fileType });\n\n    this.updateProgress({\n      transferId: this.receivingMetadata.transferId,\n      bytesTransferred: this.receivingMetadata.fileSize,\n      totalBytes: this.receivingMetadata.fileSize,\n      percentage: 100,\n      status: \"completed\",\n    });\n\n    if (this.onTransferComplete) {\n      this.onTransferComplete(blob, this.receivingMetadata);\n    }\n\n    // Auto-download the file\n    this.downloadFile(blob, this.receivingMetadata.fileName);\n\n    // Reset state\n    this.receivingMetadata = null;\n    this.receivedChunks.clear();\n  }\n\n  private downloadFile(blob: Blob, fileName: string) {\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = fileName;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  }\n\n  private updateProgress(progress: FileTransferProgress) {\n    if (this.onProgressUpdate) {\n      this.onProgressUpdate(progress);\n    }\n  }\n\n  // Event handlers\n  onProgress(callback: (progress: FileTransferProgress) => void) {\n    this.onProgressUpdate = callback;\n  }\n\n  onComplete(callback: (file: Blob, metadata: FileTransferMetadata) => void) {\n    this.onTransferComplete = callback;\n  }\n\n  onError(callback: (error: string) => void) {\n    this.onTransferError = callback;\n  }\n}\n", "import { useState, useEffect, useCallback, useRef } from \"react\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport {\n  Device,\n  FileTransferRequest,\n  FileTransferProgress,\n  SignalingMessage,\n} from \"@/types\";\nimport { SignalingClient } from \"@/lib/signaling\";\nimport { WebRTCManager } from \"@/lib/webrtc\";\nimport { FileTransferManager } from \"@/lib/fileTransfer\";\n\nexport function useShareWeb(deviceName: string) {\n  const [devices, setDevices] = useState<Device[]>([]);\n  const [transferRequests, setTransferRequests] = useState<\n    FileTransferRequest[]\n  >([]);\n  const [activeTransfers, setActiveTransfers] = useState<\n    FileTransferProgress[]\n  >([]);\n  const [isConnected, setIsConnected] = useState(false);\n\n  const signalingRef = useRef<SignalingClient | null>(null);\n  const webrtcConnections = useRef<Map<string, WebRTCManager>>(new Map());\n  const fileTransferManagers = useRef<Map<string, FileTransferManager>>(\n    new Map()\n  );\n  const pendingFileTransfers = useRef<\n    Map<string, { file: File; transferId: string }>\n  >(new Map());\n  const deviceIdRef = useRef<string>(uuidv4());\n\n  useEffect(() => {\n    const initializeSignaling = async () => {\n      try {\n        const signaling = new SignalingClient(deviceIdRef.current, deviceName);\n        signalingRef.current = signaling;\n\n        // Handle device updates\n        signaling.onDevices((discoveredDevices) => {\n          setDevices(discoveredDevices);\n        });\n\n        // Handle signaling messages\n        signaling.onSignalingMessage(handleSignalingMessage);\n\n        await signaling.start();\n        setIsConnected(true);\n      } catch (error) {\n        console.error(\"Failed to initialize signaling:\", error);\n        setIsConnected(false);\n      }\n    };\n\n    initializeSignaling();\n\n    return () => {\n      if (signalingRef.current) {\n        signalingRef.current.stop();\n      }\n\n      // Clean up WebRTC connections\n      webrtcConnections.current.forEach((connection) => connection.close());\n      webrtcConnections.current.clear();\n      fileTransferManagers.current.clear();\n    };\n  }, [deviceName]);\n\n  const handleSignalingMessage = useCallback(\n    async (message: SignalingMessage) => {\n      const { type, from, data } = message;\n\n      switch (type) {\n        case \"transfer-request\":\n          handleTransferRequest(from, data);\n          break;\n\n        case \"transfer-response\":\n          handleTransferResponse(from, data);\n          break;\n\n        case \"offer\":\n          await handleOffer(from, data);\n          break;\n\n        case \"answer\":\n          await handleAnswer(from, data);\n          break;\n\n        case \"ice-candidate\":\n          await handleIceCandidate(from, data);\n          break;\n      }\n    },\n    []\n  );\n\n  const handleTransferRequest = (senderId: string, data: any) => {\n    const senderDevice = devices.find((d) => d.id === senderId);\n    const request: FileTransferRequest = {\n      id: data.transferId,\n      senderId,\n      senderName: senderDevice?.name || \"Unknown Device\",\n      fileName: data.fileName,\n      fileSize: data.fileSize,\n      fileType: data.fileType,\n      timestamp: Date.now(),\n    };\n\n    setTransferRequests((prev) => [...prev, request]);\n  };\n\n  const handleTransferResponse = async (receiverId: string, data: any) => {\n    if (data.accepted) {\n      // Start WebRTC connection\n      await initiateWebRTCConnection(receiverId, data.transferId);\n    } else {\n      // Transfer was rejected - clean up pending transfer\n      console.log(\"Transfer rejected by\", receiverId);\n      pendingFileTransfers.current.delete(receiverId);\n    }\n  };\n\n  const handleOffer = async (\n    senderId: string,\n    offer: RTCSessionDescriptionInit\n  ) => {\n    console.log(\"Handling offer from\", senderId);\n    const webrtc = new WebRTCManager();\n    webrtcConnections.current.set(senderId, webrtc);\n\n    // Set up connection state monitoring\n    webrtc.onConnectionState((state) => {\n      console.log(`Connection state with ${senderId}:`, state);\n      if (state === \"connected\") {\n        setIsConnected(true);\n      } else if (state === \"failed\" || state === \"disconnected\") {\n        setIsConnected(false);\n      }\n    });\n\n    // Set up error handling\n    webrtc.onErrorOccurred((error) => {\n      console.error(`WebRTC error with ${senderId}:`, error);\n      // Remove failed connection\n      webrtcConnections.current.delete(senderId);\n      fileTransferManagers.current.delete(senderId);\n    });\n\n    // Set up file transfer manager\n    const fileTransfer = new FileTransferManager(webrtc);\n    fileTransferManagers.current.set(senderId, fileTransfer);\n\n    fileTransfer.onProgress((progress) => {\n      setActiveTransfers((prev) => {\n        const index = prev.findIndex(\n          (t) => t.transferId === progress.transferId\n        );\n        if (index >= 0) {\n          const updated = [...prev];\n          updated[index] = progress;\n          return updated;\n        } else {\n          return [...prev, progress];\n        }\n      });\n    });\n\n    // Set up WebRTC signaling\n    webrtc.onSignaling(async (signalingMessage) => {\n      if (signalingRef.current) {\n        await signalingRef.current.sendMessage(senderId, signalingMessage);\n      }\n    });\n\n    try {\n      // Create answer\n      const answer = await webrtc.createAnswer(offer);\n\n      if (signalingRef.current) {\n        await signalingRef.current.sendMessage(senderId, {\n          type: \"answer\",\n          data: answer,\n        });\n      }\n    } catch (error) {\n      console.error(\"Failed to create answer:\", error);\n      webrtcConnections.current.delete(senderId);\n      fileTransferManagers.current.delete(senderId);\n    }\n  };\n\n  const handleAnswer = async (\n    receiverId: string,\n    answer: RTCSessionDescriptionInit\n  ) => {\n    const webrtc = webrtcConnections.current.get(receiverId);\n    if (webrtc) {\n      await webrtc.setRemoteAnswer(answer);\n    }\n  };\n\n  const handleIceCandidate = async (\n    peerId: string,\n    candidate: RTCIceCandidateInit\n  ) => {\n    const webrtc = webrtcConnections.current.get(peerId);\n    if (webrtc) {\n      await webrtc.addIceCandidate(candidate);\n    }\n  };\n\n  const initiateWebRTCConnection = async (\n    receiverId: string,\n    transferId: string\n  ) => {\n    console.log(\"Initiating WebRTC connection to\", receiverId);\n    const webrtc = new WebRTCManager();\n    webrtcConnections.current.set(receiverId, webrtc);\n\n    // Set up connection state monitoring\n    webrtc.onConnectionState((state) => {\n      console.log(`Connection state with ${receiverId}:`, state);\n      if (state === \"connected\") {\n        setIsConnected(true);\n      } else if (state === \"failed\" || state === \"disconnected\") {\n        setIsConnected(false);\n      }\n    });\n\n    // Set up error handling\n    webrtc.onErrorOccurred((error) => {\n      console.error(`WebRTC error with ${receiverId}:`, error);\n      // Remove failed connection and clean up pending transfers\n      webrtcConnections.current.delete(receiverId);\n      fileTransferManagers.current.delete(receiverId);\n      pendingFileTransfers.current.delete(receiverId);\n    });\n\n    // Set up file transfer manager\n    const fileTransfer = new FileTransferManager(webrtc);\n    fileTransferManagers.current.set(receiverId, fileTransfer);\n\n    fileTransfer.onProgress((progress) => {\n      setActiveTransfers((prev) => {\n        const index = prev.findIndex(\n          (t) => t.transferId === progress.transferId\n        );\n        if (index >= 0) {\n          const updated = [...prev];\n          updated[index] = progress;\n          return updated;\n        } else {\n          return [...prev, progress];\n        }\n      });\n    });\n\n    // Set up data channel ready callback to send pending file\n    webrtc.onDataChannelReady(() => {\n      const pendingTransfer = pendingFileTransfers.current.get(receiverId);\n      if (pendingTransfer) {\n        console.log(\n          `Data channel ready, sending pending file for device ${receiverId}`\n        );\n        fileTransfer.sendFile(pendingTransfer.file, pendingTransfer.transferId);\n        // Clean up pending transfer\n        pendingFileTransfers.current.delete(receiverId);\n      }\n    });\n\n    // Set up WebRTC signaling\n    webrtc.onSignaling(async (signalingMessage) => {\n      if (signalingRef.current) {\n        await signalingRef.current.sendMessage(receiverId, signalingMessage);\n      }\n    });\n\n    try {\n      // Create offer\n      const offer = await webrtc.createOffer();\n\n      if (signalingRef.current) {\n        await signalingRef.current.sendMessage(receiverId, {\n          type: \"offer\",\n          data: offer,\n        });\n      }\n    } catch (error) {\n      console.error(\"Failed to create offer:\", error);\n      webrtcConnections.current.delete(receiverId);\n      fileTransferManagers.current.delete(receiverId);\n      pendingFileTransfers.current.delete(receiverId);\n    }\n  };\n\n  const sendFile = async (file: File, targetDevice: Device) => {\n    const transferId = uuidv4();\n\n    try {\n      // Store the pending file transfer\n      pendingFileTransfers.current.set(targetDevice.id, { file, transferId });\n\n      // Send transfer request\n      if (signalingRef.current) {\n        await signalingRef.current.sendMessage(targetDevice.id, {\n          type: \"transfer-request\",\n          data: {\n            transferId,\n            fileName: file.name,\n            fileSize: file.size,\n            fileType: file.type,\n          },\n        });\n      }\n\n      console.log(`Stored pending file transfer for device ${targetDevice.id}`);\n    } catch (error) {\n      console.error(\"Failed to send file:\", error);\n      // Clean up pending transfer on error\n      pendingFileTransfers.current.delete(targetDevice.id);\n      throw error;\n    }\n  };\n\n  const acceptTransfer = async (requestId: string) => {\n    const request = transferRequests.find((r) => r.id === requestId);\n    if (!request || !signalingRef.current) return;\n\n    // Send acceptance response\n    await signalingRef.current.sendMessage(request.senderId, {\n      type: \"transfer-response\",\n      data: {\n        transferId: requestId,\n        accepted: true,\n      },\n    });\n\n    // Remove from requests\n    setTransferRequests((prev) => prev.filter((r) => r.id !== requestId));\n  };\n\n  const rejectTransfer = async (requestId: string) => {\n    const request = transferRequests.find((r) => r.id === requestId);\n    if (!request || !signalingRef.current) return;\n\n    // Send rejection response\n    await signalingRef.current.sendMessage(request.senderId, {\n      type: \"transfer-response\",\n      data: {\n        transferId: requestId,\n        accepted: false,\n      },\n    });\n\n    // Remove from requests\n    setTransferRequests((prev) => prev.filter((r) => r.id !== requestId));\n  };\n\n  return {\n    devices,\n    transferRequests,\n    activeTransfers,\n    isConnected,\n    deviceId: deviceIdRef.current,\n    sendFile,\n    acceptTransfer,\n    rejectTransfer,\n  };\n}\n", "import { useState, useCallback } from 'react';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'info' | 'warning';\n  title: string;\n  message: string;\n  duration?: number;\n}\n\nexport function useNotifications() {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n\n  const addNotification = useCallback((\n    type: Notification['type'],\n    title: string,\n    message: string,\n    duration?: number\n  ) => {\n    const id = uuidv4();\n    const notification: Notification = {\n      id,\n      type,\n      title,\n      message,\n      duration,\n    };\n\n    setNotifications(prev => [...prev, notification]);\n    return id;\n  }, []);\n\n  const removeNotification = useCallback((id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  }, []);\n\n  const success = useCallback((title: string, message: string, duration?: number) => {\n    return addNotification('success', title, message, duration);\n  }, [addNotification]);\n\n  const error = useCallback((title: string, message: string, duration?: number) => {\n    return addNotification('error', title, message, duration);\n  }, [addNotification]);\n\n  const info = useCallback((title: string, message: string, duration?: number) => {\n    return addNotification('info', title, message, duration);\n  }, [addNotification]);\n\n  const warning = useCallback((title: string, message: string, duration?: number) => {\n    return addNotification('warning', title, message, duration);\n  }, [addNotification]);\n\n  const clear = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  return {\n    notifications,\n    addNotification,\n    removeNotification,\n    success,\n    error,\n    info,\n    warning,\n    clear,\n  };\n}\n"], "names": [], "mappings": "oJAEA,EAAA,EAAA,CAAA,CAAA,OCKe,SAAS,EAAW,cAAE,CAAY,CAAE,cAAY,CAAmB,EAChF,IAAM,EAAe,CAAA,EAAA,EAAA,MAAA,AAAM,EAAmB,MAqB9C,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oEAA2D,yBAIzE,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,IAAK,EACL,KAAK,OACL,SA5BoB,AAAD,CA4BT,GA3Bd,IAAM,EAAO,EAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,CAChC,GACF,EAAa,CADL,CAGZ,EAwBM,UAAU,SACV,OAAO,QAGT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,QA3Bc,CA2BL,IA1Bb,EAAa,OAAO,EAAE,OACxB,EA0BM,UAAU,oLAET,EACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oBAAoB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC3E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,sDAGzE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,6DACV,EAAa,IAAI,GAEpB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,qDACV,CAtCQ,AAAC,IACtB,GAAc,IAAV,EAAa,MAAO,UAGxB,IAAM,EAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAS,KAAK,GAAG,CAAC,OAChD,OAAO,WAAW,CAAC,EAAQ,KAAK,GAAG,CAAC,AAH1B,KAG6B,EAAA,CAAE,CAAE,OAAO,CAAC,IAAM,IAF3C,AAEiD,CAFhD,QAAS,KAAM,KAAM,KAAK,AAE2B,CAAC,EACvE,AADyE,GAiC3C,EAAa,IAAI,EAAE,MAAI,EAAa,IAAI,EAAI,qBAGhE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oDAA2C,wCAK1D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oBAAoB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC3E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,8FAGzE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,6DAAoD,2BAGjE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oDAA2C,iDAQ/D,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,qDACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uBAAc,oBAAsB,iEAMhE,CCtFe,SAAS,EAAW,CAAE,SAAO,gBAAE,CAAc,cAAE,CAAY,CAAmB,EAqC3F,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+DAAsD,mBAGpE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oDACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oDAA2C,sBAI3C,IAAnB,EAAQ,MAAM,CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oBAAoB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC3E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gJAGzE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iDAAwC,qBAGrD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oDAA2C,oFAK1D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAQ,GAAG,CAAC,AAAC,GACZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAEC,QAAS,IAAM,GAAgB,EAAe,GAC9C,UAAW,CAAC,uEAAuE,EACjF,EACI,kJACA,qEAAA,CACJ,WAEF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAA,EAAG,EAAO,QAAQ,CAAG,qCAAuC,mCAAA,CAAoC,UAC7G,CA5EK,AAAC,IACrB,IAAM,EAAO,EAAW,WAAW,UACnC,AAAI,EAAK,QAAQ,CAAC,WAAa,EAAK,QAAQ,CAAC,WAEzC,CAFqD,AAErD,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,kFAGhE,EAAK,QAAQ,CAAC,SAAW,EAAK,QAAQ,CAAC,UAE9C,CAFyD,AAEzD,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6MAKvE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gHAI7E,EAuD+B,EAAO,IAAI,IAE5B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,qDACV,EAAO,IAAI,GAEd,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oDACV,CA5DU,AAAC,IAC5B,IAEM,EAAU,AAFV,KAEe,KAAK,CAAC,CADd,AADD,KAAK,GAAG,GACD,CAAA,EACe,KAC5B,EAAU,KAAK,KAAK,CAAC,EAAU,IAC/B,EAAQ,KAAK,KAAK,CAAC,EAAU,WAE/B,AAAJ,EAAc,GAAW,CAAP,UACd,EAAU,GAAW,CAAP,AAAO,EAAG,EAAQ,KAAK,CAAC,CACtC,EAAQ,GAAW,CAAP,AAAO,EAAG,EAAM,KAAK,CAAC,CAC/B,UACT,EAiDwC,EAAO,QAAQ,UAK3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CAAC,qBAAqB,EACpC,EAAO,QAAQ,CAAG,eAAiB,cAAA,CACnC,GACD,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC/E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wBA5BtE,EAAO,EAAE,KAqCrB,CAAC,GAAgB,EAAQ,MAAM,CAAG,GACjC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kEACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,yDACX,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,uBAAc,wBAA0B,8CAMpE,CCnHe,SAAS,EAAiB,UAAE,CAAQ,CAAyB,EAC1E,IAAM,EAAc,AAAC,IACnB,GAAc,IAAV,EAAa,MAAO,UAGxB,IAAM,EAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAS,KAAK,GAAG,CAAC,OAChD,OAAO,WAAW,CAAC,EAAQ,KAAK,GAAG,CAAC,AAH1B,KAG6B,EAAA,CAAE,CAAE,OAAO,CAAC,IAAM,IAF3C,AAEiD,CAFhD,QAAS,KAAM,KAAM,KAEgC,AAF3B,CAE4B,EAAE,AACzE,EAwEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2GACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAW,CA1ED,AAAC,IACtB,OAAQ,GACN,IAAK,YAEL,IAAK,eADH,MAAO,kCAGT,KAAK,YACH,MAAO,oCACT,KAAK,SACH,MAAO,gCACT,KAAK,IACH,MAAO,kCAGX,EACF,EA2DuC,EAAS,MAAM,WAC3C,CA1DW,AAAC,IACrB,OAAQ,GACN,IAAK,YACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBAAuB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC9E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,iHAG3E,KAAK,eACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yFAG3E,KAAK,YACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,mDAG3E,KAAK,SACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,uDAG3E,KAAK,YACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,0EAG3E,SACE,OAAO,IACX,EACF,EAuByB,EAAS,MAAM,IAEhC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,sDAA4C,YAC7C,EAAS,UAAU,CAAC,KAAK,CAAC,EAAG,GAAG,SAE5C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,+DACV,EAAS,MAAM,SAKtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,8DACV,EAAS,UAAU,CAAC,OAAO,CAAC,GAAG,OAElC,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,qDACV,EAAY,EAAS,gBAAgB,EAAE,MAAI,EAAY,EAAS,UAAU,WAMjF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAC,6CAA6C,EAAE,CA9CvC,AAAC,IAC3B,OAAQ,GACN,IAAK,YACL,IAAK,eACH,MAAO,aACT,KAAK,YACH,MAAO,cACT,KAAK,SACH,MAAO,YACT,KAAK,IACH,MAAO,aAGX,EACF,EAgCuF,EAAS,MAAM,EAAA,CAAG,CACjG,MAAO,CAAE,MAAO,CAAA,EAAG,KAAK,GAAG,CAAC,EAAS,UAAU,CAAE,KAAK,CAAC,CAAC,AAAC,MAK5D,AAAoB,mBAAX,MAAM,EACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,oBACN,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,sBAIW,cAApB,EAAS,MAAM,EACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4EACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,oCACN,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,kBAIW,WAApB,EAAS,MAAM,EACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wEACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,oBACN,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,yBAKhB,CCvIe,SAAS,EAAgB,CAAE,SAAO,UAAE,CAAQ,CAAE,UAAQ,CAAwB,MAStE,EA6CrB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6GACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAhDrB,AAAI,CAiDK,EAAY,EAAQ,QAAQ,EAjDxB,UAAU,CAAC,UAEpB,CAF+B,AAE/B,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC/E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gKAGhE,EAAS,UAAU,CAAC,UAE3B,CAFsC,AAEtC,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0BAA0B,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yIAGhE,EAAS,UAAU,CAAC,UAE3B,CAFsC,AAEtC,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAyB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAChF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,0JAGhE,EAAS,QAAQ,CAAC,OAEzB,CAFiC,AAEjC,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBAAuB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC9E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,iHAKvE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAwB,KAAK,OAAO,OAAO,eAAe,QAAQ,qBAC/E,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6HAwBrE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,8DAAoD,sBAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4CAAoC,EAAQ,UAAU,MAE3F,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yDACV,CAvBG,AAAC,IACf,IAEM,EAFA,AAEU,KAAK,KAAK,CAAC,CAFf,AACC,KADI,GAAG,GACD,CAAA,EACe,KAC5B,EAAU,KAAK,KAAK,CAAC,EAAU,WAErC,AAAI,EAAU,GAAW,CAAP,UACd,EAAU,GAAW,CAAP,AAAO,EAAG,EAAQ,KAAK,CAAC,CACnC,cACT,EAcqB,EAAQ,SAAS,UAKhC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mDACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,gEAAuD,kBAM3E,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8DACV,EAAQ,QAAQ,GAEnB,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,qDACV,CArFO,AAAC,IACnB,GAAI,AAAU,MAAG,MAAO,UAGxB,IAAM,EAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAS,KAAK,GAAG,CAAC,OAChD,OAAO,WAAW,CAAC,EAAQ,KAAK,GAAG,CAHzB,AAG0B,KAAG,EAAA,CAAE,CAAE,OAAO,CAAC,IAAM,IAAM,AAFjD,CAAC,QAAS,KAAM,KAAM,KAEgC,AAF3B,CAE4B,EAAE,CACzE,EA+EyB,EAAQ,QAAQ,EAAE,MAAI,EAAQ,QAAQ,EAAI,yBAM/D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oDAA2C,sCAIxD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAS,EAAQ,EAAE,EAClC,UAAU,6KACX,YAGD,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAS,EAAQ,EAAE,EAClC,UAAU,4IAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,qBACjE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,oDAEvE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,sBAMlB,CClHe,SAAS,EAAa,IACnC,CAAE,MACF,CAAI,OACJ,CAAK,SACL,CAAO,UACP,EAAW,GAAI,SACf,CAAO,CACW,EAClB,GAAM,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAE3C,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,IAAM,EAAQ,WAAW,KACvB,GAAa,GACb,WAAW,IAAM,EAAQ,GAAK,IAChC,EAAG,AADmC,GAGtC,MAAO,IAAM,aAAa,EAC5B,EAAG,CAAC,EAAI,AAJiE,EAIvD,EAAQ,EAsF1B,IAAM,EAAe,KACnB,OAAQ,GACN,IAAK,UACH,MAAO,oCACT,KAAK,QACH,MAAO,gCACT,KAAK,UACH,MAAO,sCACT,KAAK,IAEH,MAAO,kCACX,CACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAC,qGAAqG,EAC/G,EAAY,4BAA8B,6BAC3C,CAAC,EAAE,CAhCmB,KACzB,OAAQ,GACN,IAAK,UACH,MAAO,yEACT,KAAK,QACH,MAAO,iEACT,KAAK,UACH,MAAO,6EACT,KAAK,IAEH,MAAO,qEACX,EACF,IAoBQ,CAAsB,UAE1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAzGL,AAyGsB,MAxGpC,OAAQ,GACN,IAAK,UACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAU,yBACV,KAAK,OACL,OAAO,eACP,QAAQ,qBAER,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,mDAIV,KAAK,QACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAU,uBACV,KAAK,OACL,OAAO,eACP,QAAQ,qBAER,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,uDAIV,KAAK,UACH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAU,0BACV,KAAK,OACL,OAAO,eACP,QAAQ,qBAER,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,+IAIV,KAAK,IAEH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAU,wBACV,KAAK,OACL,OAAO,eACP,QAAQ,qBAER,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,+DAIZ,CACF,OAsCM,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAW,CAAC,oBAAoB,EAAE,IAAA,CAAgB,UAAG,IACxD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAW,CAAC,aAAa,EAAE,IAAe,WAAW,CAAC,UACtD,OAGL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,KACP,GAAa,GACb,WAAW,IAAM,EAAQ,GAAK,IAChC,EACA,UAAW,CAAC,iFAAiF,EAAE,IAAe,iBAAiB,CAAC,UAEhI,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAU,UACV,KAAK,OACL,OAAO,eACP,QAAQ,qBAER,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,mCAQlB,CAgBO,SAAS,EAAsB,eACpC,CAAa,SACb,CAAO,CACoB,EAC3B,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACZ,EAAc,GAAG,CAAC,CAAC,EAAc,IAChC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAEC,MAAO,CAAE,IAAK,CAAA,EAAW,GAAR,EAAW,EAAE,CAAC,AAAC,EAChC,UAAU,oBAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAc,GAAG,CAAY,CAAE,QAAS,KAJpC,EAAa,EAAE,IAS9B,CCzMA,IAAA,EAAA,EAAA,CAAA,CAAA,aACe,CAAE,WAAA,EAAA,UAAU,AAAC,ECAtB,EAAY,IAAI,WAAW,KAC7B,EAAU,EAAU,MAAM,CGDxB,EAAY,EAAE,CACpB,IAAK,IAAI,EAAI,EAAG,EAAI,IAAK,EAAE,EAAG,AAC1B,EAAU,IAAI,CAAC,CAAC,EAAI,GAAA,CAAK,CAAE,QAAQ,CAAC,IAAI,KAAK,CAAC,UCAlD,SAAS,AAAG,CAAO,CAAE,CAAG,CAAE,CAAM,EAC5B,GAAI,EAAO,UAAU,CAsBV,CAtBc,CAAC,GAAO,CAAC,EAC9B,OADuC,AAChC,EAAO,UAAU,GAG5B,IAAM,EAAO,CADb,EAAU,GAAW,EAAC,EACD,MAAM,EAAI,EAAQ,GAAG,OJJtC,CII8C,CJJpC,EAAU,MAAM,CAAG,IAAI,CACjC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GACf,EAAU,GAEP,EAAU,KAAK,CAAC,EAAU,GAAW,KIC5C,GAAI,EAAK,MAAM,CAAG,GACd,CADkB,KACZ,AAAI,MAAM,qCAIpB,GAFA,CAAI,CAAC,EAAE,CAAc,GAAV,CAAI,CAAC,EAAE,CAAW,GAC7B,CAAI,CAAC,EAAE,CAAI,AAAU,IAAN,CAAC,EAAE,CAAW,IACzB,EAAK,CAEL,GAAI,CADJ,EAAS,IAAU,EACN,GAAK,EAAS,GAAK,EAAI,MAAM,CACtC,CADwC,KAClC,AAAI,WAAW,CAAC,gBAAgB,EAAE,EAAO,CAAC,EAAE,EAAS,GAAG,wBAAwB,CAAC,EAE3F,IAAK,IAAI,EAAI,EAAG,EAAI,GAAI,EAAE,EAAG,AACzB,CAAG,CAAC,EAAS,EAAE,CAAG,CAAI,CAAC,EAAE,CAE7B,OAAO,CACX,CACA,ODnBG,ACmBI,SDnBK,AAAgB,CAAG,CAAE,EAAS,CAAC,EAC3C,MAAO,CAAC,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC9B,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,IACA,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,IACA,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,IACA,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,CAAS,CAAC,CAAG,CAAC,EAAS,EAAE,CAAC,CAC1B,IACA,CAAS,CAAC,CAAG,CAAC,EAAS,GAAG,CAAC,CAC3B,CAAS,CAAC,CAAG,CAAC,EAAS,GAAG,CAAC,CAC3B,CAAS,CAAC,CAAG,CAAC,EAAS,GAAG,CAAC,CAC3B,CAAS,CAAC,CAAG,CAAC,EAAS,GAAG,CAAC,CAC3B,CAAS,CAAC,CAAG,CAAC,EAAS,GAAG,CAAC,CAC3B,CAAS,CAAC,CAAG,CAAC,EAAS,GAAG,CAAC,EAAE,WAAW,EAChD,ECF2B,EAC3B,CCvBO,OAAM,EACH,QAAiB,CACjB,UAAmB,CACnB,OAAgB,CAChB,gBAAyC,IAAK,CAC9C,kBAA2C,IAAK,CAChD,gBAAwD,IAAK,CAC7D,UAA0D,IAAK,CAC/D,WAAY,CAAM,AAE1B,aAAY,CAAgB,CAAE,CAAkB,CAAE,EAAU,gBAAgB,CAAE,CAC5E,IAAI,CAAC,QAAQ,CAAG,EAChB,IAAI,CAAC,UAAU,CAAG,EAClB,IAAI,CAAC,OAAO,CAAG,CACjB,CAEA,MAAM,OAAuB,CAC3B,GAAI,CAEF,MAAM,IAAI,CAAC,QAAQ,GAGnB,IAAI,CAAC,YAAY,GAGjB,IAAI,CAAC,cAAc,GAGnB,IAAI,CAAC,oBAAoB,GAEzB,QAAQ,GAAG,CAAC,2BACd,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,oCAAqC,GAC7C,CACR,CACF,CAEA,MAAM,MAAsB,CAC1B,IAAI,CAAC,SAAS,CAAG,GAEb,IAAI,CAAC,eAAe,EAAE,CACxB,cAAc,IAAI,CAAC,eAAe,EAClC,IAAI,CAAC,eAAe,CAAG,MAGrB,IAAI,CAAC,iBAAiB,EAAE,CAC1B,cAAc,IAAI,CAAC,iBAAiB,EACpC,IAAI,CAAC,iBAAiB,CAAG,MAG3B,GAAI,CAEF,MAAM,MAAM,CAAA,EAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAA,CAAE,CAAE,CACvD,OAAQ,QACV,EACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,+BAAgC,EAChD,CAEA,QAAQ,GAAG,CAAC,2BACd,CAEA,MAAc,UAA0B,CAatC,GAAI,CAZa,AAYZ,OAZkB,MAAM,IAAI,CAAC,OAAO,CAAE,CACzC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,OAAQ,WACR,SAAU,IAAI,CAAC,QAAQ,CACvB,WAAY,IAAI,CAAC,UAAU,AAC7B,EACF,EAAA,EAEc,EAAE,CACd,CADgB,KACV,AAAI,MAAM,4BAEpB,CAEQ,cAAqB,CAC3B,IAAI,CAAC,SAAS,EAAG,EACjB,IAAI,CAAC,IAAI,EACX,CAEA,MAAc,MAAsB,CAClC,GAAK,CAAD,GAAK,CAAC,SAAS,EAAE,AAErB,GAAI,CACF,IAAM,EAAW,MAAM,MACrB,CAAA,EAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CACvD,CACE,OAAQ,KACV,GAGF,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAE5B,EAAK,QAAQ,EAAI,EAAK,QAAQ,CAAC,MAAM,CAAG,GAAG,AAC7C,EAAK,QAAQ,CAAC,OAAO,CAAC,AAAC,IACjB,IAAI,CAAC,SAAS,EAAE,AAClB,IAAI,CAAC,SAAS,CAAC,EAEnB,EAEJ,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,iBAAkB,EAClC,CAGI,IAAI,CAAC,SAAS,EAAE,AAClB,WAAW,IAAM,IAAI,CAAC,IAAI,GAAI,KAElC,CAEQ,gBAAuB,CAC7B,IAAI,CAAC,iBAAiB,CAAG,YAAY,UACnC,GAAI,CACF,MAAM,MAAM,IAAI,CAAC,OAAO,CAAE,CACxB,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,OAAQ,YACR,SAAU,IAAI,CAAC,QACjB,AADyB,EAE3B,EACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,mBAAoB,EACpC,CACF,EAAG,IACL,CAEQ,GAHK,mBAGwB,CACnC,IAAM,EAAkB,QAJqB,EAK3C,GAAI,CACF,IAAM,EAAW,MAAM,MACrB,CAAA,EAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAC1D,CACE,OAAQ,KACV,GAGF,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAC5B,IAAI,CAAC,eAAe,EAAI,EAAK,OAAO,EAAE,AACxC,IAAI,CAAC,eAAe,CAAC,EAAK,OAAO,CAErC,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0BAA2B,EAC3C,CACF,EAGA,IACA,YAAY,EAAiB,IAC/B,CAEA,MAAM,YAAY,CAAgB,CAAE,CAAqD,CAAiB,CACxG,GAAI,CAkBF,GAAI,CAAC,CAjBY,MAAM,MAAM,IAAI,CAAC,OAAO,CAAE,CACzC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,OAAQ,OACR,SAAU,IAAI,CAAC,QAAQ,UACvB,EACA,QAAS,CACP,GAAG,CAAO,CACV,KAAM,IAAI,CAAC,QAAQ,CACnB,UAAW,KAAK,GAAG,EACrB,CACF,EACF,EAAA,EAEc,EAAE,CACd,CADgB,KACV,AAAI,MAAM,yBAEpB,CAAE,MAAO,EAAO,CAEd,MADA,QAAQ,KAAK,CAAC,0BAA2B,GACnC,CACR,CACF,CAGA,UAAU,CAAqC,CAAQ,CACrD,IAAI,CAAC,eAAe,CAAG,CACzB,CAEA,mBAAmB,CAA6C,CAAQ,CACtE,IAAI,CAAC,SAAS,CAAG,CACnB,CAEA,aAAsB,CACpB,OAAO,IAAI,CAAC,QAAQ,AACtB,CAEA,eAAwB,CACtB,OAAO,IAAI,CAAC,UACd,AADwB,CAE1B,CC9MO,IAAM,EAA8B,CACzC,WAAY,CACV,CAAE,KAAM,8BAA+B,EACvC,CAAE,KAAM,6BAA8B,EACtC,CAAE,KAAM,8BAA+B,EACvC,CAAE,KAAM,8BAA+B,EACvC,CAAE,KAAM,+BAAgC,EACxC,CAAE,KAAM,8BAA+B,EACvC,CAAE,KAAM,8BAA+B,EACvC,CAAE,KAAM,8BAA+B,EACvC,CAAE,KAAM,+BAAgC,EACxC,CAAE,KAAM,8BAA+B,EACxC,CACD,qBAAsB,GACtB,aAAc,aACd,cAAe,SACjB,CAEO,OAAM,EACH,eAA2C,IAAK,CAChD,YAAqC,IAAK,CAC1C,mBACN,IAAK,AACC,mBAAyC,IAAK,CAC9C,qBAA6D,IAAK,CAClE,mBAA0C,IAAK,CAC/C,wBAEG,IAAK,CACR,QAA4C,IAAK,CAGjD,gBAA0C,KAAM,CAChD,kBAA0C,KAAM,CAChD,kBAAoB,CAAE,AACtB,sBAAuB,CAAE,CACzB,iBAA0C,IAAK,CAC/C,aAAc,CAAM,AAE5B,cAAc,CACZ,IAAI,CAAC,mBAAmB,EAC1B,CAEQ,qBAAsB,CAC5B,IAAI,CAAC,cAAc,CAAG,IAAI,kBAAkB,GAG5C,IAAI,CAAC,cAAc,CAAC,cAAc,CAAG,AAAC,IAChC,EAAM,SAAS,EAAI,IAAI,CAAC,kBAAkB,EAC5C,AAD8C,IAC1C,CAAC,kBAAkB,CAAC,CACtB,KAAM,gBACN,KAAM,GACN,KAAM,EAAM,SAAS,CACrB,UAAW,KAAK,GAAG,EACrB,EAEJ,EAGA,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAG,KAC1C,IAAI,CAAC,cAAc,EAAE,CACvB,IAAI,CAAC,iBAAiB,CAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAC9D,QAAQ,GAAG,CAAC,uBAAwB,IAAI,CAAC,iBAAiB,EAE9D,EAGA,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAG,KAC3C,IAAI,CAAC,cAAc,EAAE,CACvB,QAAQ,GAAG,CACT,wBACA,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAIK,WAA3C,IAAI,CAAC,cAAc,CAAC,kBAAkB,EACK,iBAA3C,IAAI,CAAC,cAAc,CAAC,kBAAkB,AAAK,GAC3C,AACA,IAAI,CAAC,uBAAuB,GAGlC,EAGA,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAG,KACxC,IAAI,CAAC,cAAc,EAAE,CACvB,IAAI,CAAC,eAAe,CAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAC1D,QAAQ,GAAG,CAAC,oBAAqB,IAAI,CAAC,eAAe,EAEjD,IAAI,CAAC,uBAAuB,EAAE,AAChC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,EAGtB,aAAa,CAAtC,IAAI,CAAC,eAAe,CACtB,IAAI,CAAC,iBAAiB,CAAG,GAEA,AAFG,WAE5B,IAAI,CAAC,eAAe,EACK,AAHoC,iBAG7D,IAAI,CAAC,eAAe,AAAK,GACzB,AACA,IAAI,CAAC,uBAAuB,GAGlC,EAGA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAG,AAAC,IACnC,IAAM,EAAU,EAAM,OAAO,CAC7B,IAAI,CAAC,gBAAgB,CAAC,EACxB,CACF,CAEQ,iBAAiB,CAAuB,CAAE,CAChD,IAAI,CAAC,WAAW,CAAG,EAGnB,EAAQ,UAAU,CAAG,cAErB,EAAQ,MAAM,CAAG,KACf,QAAQ,GAAG,CAAC,oCAAqC,EAAQ,UAAU,EAC/D,IAAI,CAAC,iBAAiB,EAAE,AAC1B,IAAI,CAAC,iBAAiB,EAE1B,EAEA,EAAQ,SAAS,CAAG,AAAC,IACnB,GAAI,CACF,GAAI,IAAI,CAAC,oBAAoB,CAAE,CAE7B,IAAI,EACJ,GAAI,EAAM,IAAI,YAAY,YACxB,CADqC,CAC9B,EAAM,IAAI,MACZ,GAAI,EAAM,IAAI,YAAY,KAAM,YAErC,EAAM,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,AAAC,IACzB,IAAI,CAAC,oBAAoB,EAAE,AAC7B,IAAI,CAAC,oBAAoB,CAAC,EAE9B,QAEK,YACL,QAAQ,IAAI,CAAC,wBAAyB,OAAO,EAAM,IAAI,EAIzD,IAAI,CAAC,oBAAoB,CAAC,EAC5B,CACF,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,yCAA0C,GACpD,IAAI,CAAC,OAAO,EAAE,AAChB,IAAI,CAAC,OAAO,CAAC,kCAEjB,CACF,EAEA,EAAQ,OAAO,CAAG,KAChB,QAAQ,GAAG,CAAC,uBACR,IAAI,CAAC,kBAAkB,EAAE,AAC3B,IAAI,CAAC,kBAAkB,EAE3B,EAEA,EAAQ,OAAO,CAAG,AAAC,IACjB,QAAQ,KAAK,CAAC,sBAAuB,GACjC,IAAI,CAAC,OAAO,EAAE,AAChB,IAAI,CAAC,OAAO,CAAC,8BAEjB,CACF,CAGA,MAAM,aAAkD,CACtD,GAAI,CAAC,IAAI,CAAC,cAAc,CACtB,CADwB,KAClB,AAAI,MAAM,mCAGlB,IAAI,CAAC,WAAW,EAAG,EAGnB,IAAI,CAAC,WAAW,CAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,eAAgB,CACvE,SAAS,EACT,eAAgB,EAChB,kBAAmB,IACnB,SAAU,kBACZ,GAEA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAEtC,IAAM,EAAQ,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAClD,qBAAqB,EACrB,qBAAqB,CACvB,GAEA,OADA,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,GACvC,CACT,CAGA,MAAM,aACJ,CAAgC,CACI,CACpC,GAAI,CAAC,IAAI,CAAC,cAAc,CACtB,CADwB,KAClB,AAAI,MAAM,kCAGlB,OAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,GAC/C,IAAM,EAAS,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,GAErD,OADA,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,GACvC,CACT,CAGA,MAAM,gBAAgB,CAAiC,CAAiB,CACtE,GAAI,CAAC,IAAI,CAAC,cAAc,CACtB,CADwB,KAClB,AAAI,MAAM,kCAGlB,OAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,EACjD,CAGA,MAAM,gBAAgB,CAA8B,CAAiB,CACnE,GAAI,CAAC,IAAI,CAAC,cAAc,CACtB,CADwB,KAClB,AAAI,MAAM,kCAGlB,OAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,EAC5C,CAGA,SAAS,CAAiB,CAAW,CACnC,GAAI,CAAC,IAAI,CAAC,WAAW,EAAoC,QAAQ,CAAxC,IAAI,CAAC,WAAW,CAAC,UAAU,CAClD,OAAO,EAGT,GAAI,CAEF,OADA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IACf,CACT,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,sBAAuB,IAC9B,CACT,CACF,CAGA,oBAA8B,CAC5B,OAAO,IAAI,CAAC,WAAW,EAAE,aAAe,MAC1C,CAGA,oBAAoD,CAClD,OAAO,IAAI,CAAC,cAAc,EAAE,iBAAmB,IACjD,CAGQ,yBAA0B,CAChC,GAAI,IAAI,CAAC,iBAAiB,EAAI,IAAI,CAAC,oBAAoB,CAAE,CACvD,QAAQ,KAAK,CAAC,qCACV,IAAI,CAAC,OAAO,EAAE,AAChB,IAAI,CAAC,OAAO,CAAC,6CAEf,MACF,CAEA,IAAI,CAAC,iBAAiB,GACtB,QAAQ,GAAG,CACT,CAAC,2CAA2C,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAA,CAAE,EAIjG,IAAI,CAAC,gBAAgB,EAAE,AACzB,aAAa,IAAI,CAAC,gBAAgB,EAIpC,IAAI,CAAC,gBAAgB,CAAG,WAAW,KACjC,IAAI,CAAC,mBAAmB,EAC1B,EAAG,IAAO,IAAI,CAAC,iBAAiB,CAClC,CAEA,CAHqC,KAGvB,iBAH6C,IAGvB,CAClC,GAAI,CACE,IAAI,CAAC,cAAc,EAAE,CAEvB,IAAI,CAAC,cAAc,CAAC,UAAU,GAC9B,QAAQ,GAAG,CAAC,yBAEhB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,yBAA0B,GACpC,IAAI,CAAC,OAAO,EAAE,AAChB,IAAI,CAAC,OAAO,CAAC,+BAEjB,CACF,CAGA,mBAAoB,QAClB,AAAK,IAAD,AAAK,CAAC,cAAc,CAEjB,CAFmB,AAGxB,gBAAiB,IAAI,CAAC,cAAc,CAAC,eAAe,CACpD,mBAAoB,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAC1D,kBAAmB,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACxD,eAAgB,IAAI,CAAC,cAAc,CAAC,cAAc,CAClD,iBAAkB,IAAI,CAAC,WAAW,EAAE,YAAc,OAClD,kBAAmB,IAAI,CAAC,iBAAiB,AAC3C,EATiC,IAUnC,CAGA,YAAY,CAA6C,CAAE,CACzD,IAAI,CAAC,kBAAkB,CAAG,CAC5B,CAEA,mBAAmB,CAAoB,CAAE,CACvC,IAAI,CAAC,iBAAiB,CAAG,CAC3B,CAEA,eAAe,CAAqC,CAAE,CACpD,IAAI,CAAC,oBAAoB,CAAG,CAC9B,CAEA,oBAAoB,CAAoB,CAAE,CACxC,IAAI,CAAC,kBAAkB,CAAG,CAC5B,CAEA,kBAAkB,CAAiD,CAAE,CACnE,IAAI,CAAC,uBAAuB,CAAG,CACjC,CAEA,gBAAgB,CAAiC,CAAE,CACjD,IAAI,CAAC,OAAO,CAAG,CACjB,CAGA,OAAQ,CAEF,IAAI,CAAC,gBAAgB,EAAE,CACzB,aAAa,IAAI,CAAC,gBAAgB,EAClC,IAAI,CAAC,gBAAgB,CAAG,MAGtB,IAAI,CAAC,WAAW,EAAE,CACpB,IAAI,CAAC,WAAW,CAAC,KAAK,GACtB,IAAI,CAAC,WAAW,CAAG,MAGjB,IAAI,CAAC,cAAc,EAAE,CACvB,IAAI,CAAC,cAAc,CAAC,KAAK,GACzB,IAAI,CAAC,cAAc,CAAG,MAIxB,IAAI,CAAC,eAAe,CAAG,MACvB,IAAI,CAAC,iBAAiB,CAAG,MACzB,IAAI,CAAC,iBAAiB,CAAG,CAC3B,CACF,CCxVO,MAAM,EACH,MAAsB,AACtB,kBACN,IAAK,CACC,mBAEG,IAAK,CACR,gBAAoD,IAAK,CAGzD,YAA2B,IAAK,CAChC,cAA6B,EAAE,AAAC,AAChC,mBAAoB,CAAE,CACtB,WAAa,EAAG,AAGhB,mBAAiD,IAAK,CACtD,eAA2C,IAAI,GAAM,CACrD,eAAiB,CAAE,AAE3B,aAAY,CAAqB,CAAE,CACjC,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,kBAAkB,EACzB,CAEQ,oBAAqB,CAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAE,AAAD,IACzB,IAAI,CAAC,kBAAkB,CAAC,EAC1B,GAEA,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KACzB,IAAI,CAAC,WAAW,EAAE,AACpB,IAAI,CAAC,YAAY,EAErB,EACF,CAGA,MAAM,SAAS,CAAU,CAAE,CAAkB,CAAiB,CAC5D,IAAI,CAAC,WAAW,CAAG,EACnB,IAAI,CAAC,UAAU,CAAG,EAClB,IAAI,CAAC,iBAAiB,CAAG,EAGzB,IAAI,CAAC,aAAa,CAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAM,GAI3C,EAAK,IAAI,CACT,EAAK,IAAI,CACT,EAAK,IAAI,CACN,IAAI,CAAC,aAAa,CAAC,MAAM,CAIxC,IAAI,CAAC,cAAc,CAAC,YAClB,EACA,iBAAkB,EAClB,WAAY,EAAK,IAAI,CACrB,WAAY,EACZ,OAAQ,WACV,GAGI,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,AACpC,IAAI,CAAC,YAAY,EAErB,CAEA,MAAc,iBACZ,CAAU,CACV,CAAkB,CACI,CACtB,IAAM,EAAsB,EAAE,CACxB,EAAc,KAAK,IAAI,CAAC,EAAK,IAAI,GAAG,KAE1C,IAAK,IAAI,EAAI,EAAG,EAAI,EAAa,IAAK,CACpC,IAAM,QAAQ,EACR,EADY,AACN,KAAK,GAAG,CAAC,QAAQ,AAAY,EAAK,IAAI,EAC5C,EAAO,EAAK,KAAK,CAAC,EAAO,GACzB,EAAc,MAAM,EAAK,WAAW,GAE1C,EAAO,IAAI,CAAC,CACV,GAAI,EACJ,MAAO,EACP,KAAM,EACN,OAAQ,IAAM,EAAc,cAC5B,CACF,EACF,CAEA,OAAO,CACT,CAEQ,cAAe,CACrB,GAAI,CAAC,IAAI,CAAC,WAAW,EAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAE,OAGrD,IAAM,EAAiC,CACrC,SAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAC/B,SAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAC/B,SAAU,IAAI,CAAC,WAAW,CAAC,IAAI,CAC/B,YAAa,IAAI,CAAC,aAAa,CAAC,MAAM,CACtC,WAAY,IAAI,CAAC,UAAU,AAC7B,EAOA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAClB,IAAI,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC,AANlB,CACtB,KAAM,WACN,KAAM,CACR,IAG4D,MAAM,EAIlE,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,IAAI,CAAC,UAAU,CAC3B,iBAAkB,EAClB,WAAY,IAAI,CAAC,WAAW,CAAC,IAAI,CACjC,WAAY,EACZ,OAAQ,cACV,GAEA,IAAI,CAAC,aAAa,EACpB,CAEQ,eAAgB,CACtB,GAAI,IAAI,CAAC,iBAAiB,EAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAE,YAEvD,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,IAAI,CAAC,UAAU,CAC3B,iBAAkB,IAAI,CAAC,WAAW,EAAE,MAAQ,EAC5C,WAAY,IAAI,CAAC,WAAW,EAAE,MAAQ,EACtC,WAAY,IACZ,OAAQ,WACV,GAIF,IAAM,EAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAGlD,EAAkB,CACtB,KAAM,kBACN,OAAQ,CACN,GAAI,EAAM,EAAE,CACZ,MAAO,EAAM,KAAK,CAClB,OAAQ,EAAM,MAAM,CACpB,YAAa,EAAM,WAAW,CAC9B,SAAU,EAAM,IAAI,CAAC,UACvB,AADiC,EAEjC,KAAM,MAAM,IAAI,CAAC,IAAI,WAAW,EAAM,IAAI,EAC5C,EAOA,GAAI,CAJY,AAIX,IAJe,CAAC,MAAM,CAAC,QAAQ,CAClC,IAAI,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC,IAAkB,MAAM,EAGpD,CACZ,QAAQ,KAAK,CAAC,uBAAwB,EAAM,KAAK,EACjD,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,IAAI,CAAC,UAAU,CAC3B,iBAAkB,EAClB,WAAY,IAAI,CAAC,WAAW,EAAE,MAAQ,EACtC,WAAY,EACZ,OAAQ,QACV,GACA,MACF,CAGA,IAAM,EAAmB,CAAC,IAAI,CAAC,iBAAiB,EAAG,CAAC,IAAI,GAClD,EAAa,KAAK,GAAG,CACxB,GAAoB,IAAI,CAAC,WAAN,AAAiB,EAAE,OAAQ,CAAC,CAAK,IACrD,KAGF,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,IAAI,CAAC,UAAU,CAC3B,iBAAkB,KAAK,GAAG,CAAC,EAAkB,IAAI,CAAC,WAAW,EAAE,MAAQ,GACvE,WAAY,IAAI,CAAC,WAAW,EAAE,MAAQ,aACtC,EACA,OAAQ,cACV,GAEA,IAAI,CAAC,iBAAiB,GAGtB,WAAW,IAAM,IAAI,CAAC,aAAa,GAAI,GACzC,CAGQ,CAJsC,kBAInB,CAAiB,CAAE,CAC5C,GAAI,CAEF,IAAM,EAAO,IAAI,QAPoE,MAOtD,MAAM,CAAC,GAChC,EAAU,KAAK,KAAK,CAAC,GAEN,YAAY,CAA7B,EAAQ,IAAI,CACd,IAAI,CAAC,cAAc,CAAC,EAAQ,IAAI,EACN,mBAAmB,CAApC,EAAQ,IAAI,CACrB,IAAI,CAAC,mBAAmB,CAAC,GACC,SAAS,CAA1B,EAAQ,IAAI,EAErB,IAAI,CAAC,iBAAiB,CAAC,EAAQ,IAAI,CAEvC,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,+BAAgC,GAE9C,IAAI,CAAC,eAAe,CAAC,EACvB,CACF,CAEQ,eAAe,CAA8B,CAAE,CACrD,IAAI,CAAC,iBAAiB,CAAG,EACzB,IAAI,CAAC,cAAc,CAAC,KAAK,GACzB,IAAI,CAAC,cAAc,CAAG,EAAS,WAAW,CAE1C,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,EAAS,UAAU,CAC/B,iBAAkB,EAClB,WAAY,EAAS,QAAQ,CAC7B,WAAY,EACZ,OAAQ,cACV,EACF,CAEQ,gBAKG,IAAK,CAER,kBAAkB,CAKzB,CAAE,CACD,IAAI,CAAC,eAAe,CAAG,CACzB,CAEQ,gBAAgB,CAAiB,CAAE,CACzC,GAAI,CAAC,IAAI,CAAC,eAAe,EAAI,CAAC,IAAI,CAAC,iBAAiB,CAAE,OAEtD,IAAM,EAAa,IAAI,CAAC,eAAe,CAAC,KAAK,CAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAY,GAGpC,IAAM,QAAmB,IAAI,CAAC,cAAc,CAAC,IAAI,CAC3C,EAD8C,AACjC,KAAK,GAAG,CACxB,EAAmB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAI,IACvD,KAGF,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAC7C,iBAAkB,KAAK,GAAG,CACxB,EACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAEjC,WAAY,IAAI,CAAC,iBAAiB,CAAC,QAAQ,YAC3C,EACA,OAAQ,cACV,GAGI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAK,IAAI,CAAC,cAAc,EAAE,AACpD,IAAI,CAAC,YAAY,GAGnB,IAAI,CAAC,eAAe,CAAG,IACzB,CAEQ,oBAAoB,CAS3B,CAAE,CACD,GAAI,CAAC,IAAI,CAAC,iBAAiB,CAAE,YAC3B,QAAQ,KAAK,CAAC,mCAIhB,GAAM,QAAE,CAAM,MAAE,CAAI,CAAE,CAAG,EACnB,EAAa,EAAO,KAAK,CAGzB,EAAY,IAAI,WAAW,GAAM,MAAM,CAG7C,GAAI,EAAU,UAAU,GAAK,EAAO,QAAQ,CAAE,YAC5C,QAAQ,KAAK,CACX,CAAC,MAAM,EAAE,EAAW,yBAAyB,EAAE,EAAO,QAAQ,CAAC,MAAM,EAAE,EAAU,UAAU,CAAA,CAAE,EAKjG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAY,GAEpC,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,EAAW,CAAC,EAAE,EAAO,WAAW,CAAG,EAAA,CAAG,EAGpE,IAAM,EAjUS,KAAK,CAiUK,IAAI,CAAC,AAjUJ,cAiUkB,AAjUJ,CAiUK,IAAI,CAC3C,EAD8C,AACjC,KAAK,GAAG,CACxB,EAAmB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAI,IACvD,KAGF,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAC7C,iBAAkB,KAAK,GAAG,CACxB,EACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAEjC,WAAY,IAAI,CAAC,iBAAiB,CAAC,QAAQ,YAC3C,EACA,OAAQ,cACV,GAGI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAK,IAAI,CAAC,cAAc,EAAE,CACpD,QAAQ,GAAG,CAAC,2CACZ,IAAI,CAAC,YAAY,GAErB,CAEQ,cAAe,CACrB,GAAI,CAAC,IAAI,CAAC,iBAAiB,CAAE,OAE7B,IAAM,EAAwB,EAAE,CAChC,IAAK,IAAI,EAAI,EAAG,EAAI,IAAI,CAAC,cAAc,CAAE,IAAK,CAC5C,IAAM,EAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAClC,IACF,EAAO,CADE,GACE,CAAC,EAEhB,CAEA,IAAM,EAAO,IAAI,KAAK,EAAQ,CAAE,KAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,AAAC,GAEtE,IAAI,CAAC,cAAc,CAAC,CAClB,WAAY,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAC7C,iBAAkB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CACjD,WAAY,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAC3C,WAAY,IACZ,OAAQ,WACV,GAEI,IAAI,CAAC,kBAAkB,EAAE,AAC3B,IAAI,CAAC,kBAAkB,CAAC,EAAM,IAAI,CAAC,iBAAiB,EAItD,IAAI,CAAC,YAAY,CAAC,EAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAGvD,IAAI,CAAC,iBAAiB,CAAG,KACzB,IAAI,CAAC,cAAc,CAAC,KAAK,EAC3B,CAEQ,aAAa,CAAU,CAAE,CAAgB,CAAE,CACjD,IAAM,EAAM,IAAI,eAAe,CAAC,GAC1B,EAAI,SAAS,aAAa,CAAC,KACjC,EAAE,IAAI,CAAG,EACT,EAAE,QAAQ,CAAG,EACb,SAAS,IAAI,CAAC,WAAW,CAAC,GAC1B,EAAE,KAAK,GACP,SAAS,IAAI,CAAC,WAAW,CAAC,GAC1B,IAAI,eAAe,CAAC,EACtB,CAEQ,eAAe,CAA8B,CAAE,CACjD,IAAI,CAAC,gBAAgB,EAAE,AACzB,IAAI,CAAC,gBAAgB,CAAC,EAE1B,CAGA,WAAW,CAAkD,CAAE,CAC7D,IAAI,CAAC,gBAAgB,CAAG,CAC1B,CAEA,WAAW,CAA8D,CAAE,CACzE,IAAI,CAAC,kBAAkB,CAAG,CAC5B,CAEA,QAAQ,CAAiC,CAAE,CACzC,IAAI,CAAC,eAAe,CAAG,CACzB,CACF,Cd9Ye,SAAS,IACtB,GAAM,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAc,MACxD,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAiB,IAErD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KAER,IAAM,EAAQ,CACZ,SACA,UACA,OACA,UACA,aACA,SACD,CAID,EAHmB,CAAA,EACjB,CAAK,CAAC,KAAK,EAEC,GAFI,CAAC,KAAK,MAAM,GAAK,EAAM,MAAM,EAAE,CAChD,CAAC,EAAE,KAAK,KAAK,CAAiB,IAAhB,KAAK,MAAM,IAAK,CAAM,CAEvC,EAAG,EAAE,EAEL,GAAM,SACJ,CAAO,kBACP,CAAgB,iBAChB,CAAe,aACf,CAAW,UACX,CAAQ,CACR,UAAQ,gBACR,CAAc,gBACd,CAAc,CACf,CAAG,Ae7BC,SAAS,AAAY,CAAkB,EAC5C,GAAM,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAW,EAAE,EAC7C,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAEtD,EAAE,EACE,CAAC,EAAiB,EAAmB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAEpD,EAAE,EACE,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAEzC,EAAe,CAAA,EAAA,EAAA,MAAA,AAAM,EAAyB,MAC9C,EAAoB,CAAA,EAAA,EAAA,MAAA,AAAM,EAA6B,IAAI,KAC3D,EAAuB,CAAA,EAAA,EAAA,MAAA,AAAM,EACjC,IAAI,KAEA,EAAuB,CAAA,EAAA,EAAA,MAAA,AAAM,EAEjC,IAAI,KACA,EAAc,CAAA,EAAA,EAAA,MAAA,AAAM,EAAS,KAEnC,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KAsBR,CArB4B,UAC1B,GAAI,CACF,IAAM,EAAY,IAAI,EAAgB,EAAY,OAAO,CAAE,EAC3D,GAAa,OAAO,CAAG,EAGvB,EAAU,SAAS,CAAC,AAAC,IACnB,EAAW,EACb,GAGA,EAAU,kBAAkB,CAAC,GAE7B,MAAM,EAAU,KAAK,GACrB,GAAe,EACjB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,kCAAmC,GACjD,GAAe,EACjB,EACF,IAIO,KACD,EAAa,OAAO,EAAE,AACxB,EAAa,OAAO,CAAC,IAAI,GAI3B,EAAkB,OAAO,CAAC,OAAO,CAAC,AAAC,GAAe,EAAW,KAAK,IAClE,EAAkB,OAAO,CAAC,KAAK,GAC/B,EAAqB,OAAO,CAAC,KAAK,EACpC,GACC,CAAC,EAAW,EAEf,IAAM,EAAyB,CAAA,EAAA,EAAA,WAAA,AAAW,EACxC,MAAO,IACL,GAAM,MAAE,CAAI,MAAE,CAAI,MAAE,CAAI,CAAE,CAAG,EAE7B,OAAQ,GACN,IAAK,mBACH,EAAsB,EAAM,GAC5B,KAEF,KAAK,oBACH,EAAuB,EAAM,GAC7B,KAEF,KAAK,QACH,MAAM,EAAY,EAAM,GACxB,KAEF,KAAK,SACH,MAAM,EAAa,EAAM,GACzB,KAEF,KAAK,gBACH,MAAM,EAAmB,EAAM,EAEnC,CACF,EACA,EAAE,EAGE,EAAwB,CAAC,EAAkB,KAC/C,IAAM,EAAe,EAAQ,IAAI,CAAC,AAAC,GAAM,EAAE,EAAE,GAAK,GAC5C,EAA+B,CACnC,GAAI,EAAK,UAAU,UACnB,EACA,WAAY,GAAc,MAAQ,iBAClC,SAAU,EAAK,QAAQ,CACvB,SAAU,EAAK,QAAQ,CACvB,SAAU,EAAK,QAAQ,CACvB,UAAW,KAAK,GAAG,EACrB,EAEA,EAAoB,AAAC,GAAS,IAAI,EAAM,EAAQ,CAClD,EAEM,EAAyB,MAAO,EAAoB,KACpD,EAAK,QAAQ,CAEf,CAFiB,KAEX,EAAyB,EAAY,EAAK,UAAU,GAG1D,QAAQ,GAAG,CAAC,uBAAwB,GACpC,EAAqB,OAAO,CAAC,MAAM,CAAC,GAExC,EAEM,EAAc,MAClB,EACA,KAEA,QAAQ,GAAG,CAAC,sBAAuB,GACnC,IAAM,EAAS,IAAI,EACnB,EAAkB,OAAO,CAAC,GAAG,CAAC,EAAU,GAGxC,EAAO,iBAAiB,CAAC,AAAC,IACxB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,EAAS,CAAC,CAAC,CAAE,GACpC,aAAa,CAAvB,EACF,GAAe,IACI,WAAV,GAAgC,iBAAV,CAAU,GAAgB,AACzD,GAAe,EAEnB,GAGA,EAAO,eAAe,CAAC,AAAC,IACtB,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,EAAS,CAAC,CAAC,CAAE,GAEhD,EAAkB,OAAO,CAAC,MAAM,CAAC,GACjC,EAAqB,OAAO,CAAC,MAAM,CAAC,EACtC,GAGA,IAAM,EAAe,IAAI,EAAoB,GAC7C,EAAqB,OAAO,CAAC,GAAG,CAAC,EAAU,GAE3C,EAAa,UAAU,CAAC,AAAC,IACvB,EAAmB,AAAC,IAClB,IAAM,EAAQ,EAAK,SAAS,CAC1B,AAAC,GAAM,EAAE,UAAU,GAAK,EAAS,UAAU,EAE7C,KAAI,IAAS,EAKX,MAAO,IAAI,EAAM,EALH,AAKY,EAJ1B,IAAM,EAAU,IAAI,EAAK,CAEzB,OADA,CAAO,CAAC,EAAM,CAAG,EACV,CACT,CAGF,EACF,GAGA,CAPW,CAOJ,WAAW,CAAC,MAAO,IACpB,EAAa,OAAO,EAAE,AACxB,MAAM,EAAa,OAAO,CAAC,WAAW,CAAC,EAAU,EAErD,GAEA,GAAI,CAEF,IAAM,EAAS,MAAM,EAAO,YAAY,CAAC,GAErC,EAAa,OAAO,EAAE,AACxB,MAAM,EAAa,OAAO,CAAC,WAAW,CAAC,EAAU,CAC/C,KAAM,SACN,KAAM,CACR,EAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,2BAA4B,GAC1C,EAAkB,OAAO,CAAC,MAAM,CAAC,GACjC,EAAqB,OAAO,CAAC,MAAM,CAAC,EACtC,CACF,EAEM,EAAe,MACnB,EACA,KAEA,IAAM,EAAS,EAAkB,OAAO,CAAC,GAAG,CAAC,GACzC,GACF,KADU,CACJ,EAAO,eAAe,CAAC,EAEjC,EAEM,EAAqB,MACzB,EACA,KAEA,IAAM,EAAS,EAAkB,OAAO,CAAC,GAAG,CAAC,GACzC,GACF,KADU,CACJ,EAAO,eAAe,CAAC,EAEjC,EAEM,EAA2B,MAC/B,EACA,KAEA,QAAQ,GAAG,CAAC,kCAAmC,GAC/C,IAAM,EAAS,IAAI,EACnB,EAAkB,OAAO,CAAC,GAAG,CAAC,EAAY,GAG1C,EAAO,iBAAiB,CAAC,AAAC,IACxB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,EAAW,CAAC,CAAC,CAAE,GACtC,aAAa,CAAvB,EACF,GAAe,IACI,WAAV,GAAgC,iBAAV,CAAU,GAAgB,AACzD,GAAe,EAEnB,GAGA,EAAO,eAAe,CAAC,AAAC,IACtB,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,EAAW,CAAC,CAAC,CAAE,GAElD,EAAkB,OAAO,CAAC,MAAM,CAAC,GACjC,EAAqB,OAAO,CAAC,MAAM,CAAC,GACpC,EAAqB,OAAO,CAAC,MAAM,CAAC,EACtC,GAGA,IAAM,EAAe,IAAI,EAAoB,GAC7C,EAAqB,OAAO,CAAC,GAAG,CAAC,EAAY,GAE7C,EAAa,UAAU,CAAC,AAAC,IACvB,EAAmB,AAAC,IAClB,IAAM,EAAQ,EAAK,SAAS,CAC1B,AAAC,GAAM,EAAE,UAAU,GAAK,EAAS,UAAU,EAE7C,KAAI,IAAS,EAKX,MAAO,IAAI,EAAM,EAAS,AALZ,EACd,IAAM,EAAU,IAAI,EAAK,CAEzB,OADA,CAAO,CAAC,EAAM,CAAG,EACV,CACT,CAGF,EACF,GAGA,CAPW,CAOJ,kBAAkB,CAAC,KACxB,IAAM,EAAkB,EAAqB,OAAO,CAAC,GAAG,CAAC,GACrD,IACF,QAAQ,GAAG,CACT,CAAC,AAFgB,oDAEoC,EAAE,EAAA,CAAY,EAErE,EAAa,QAAQ,CAAC,EAAgB,IAAI,CAAE,EAAgB,UAAU,EAEtE,EAAqB,OAAO,CAAC,MAAM,CAAC,GAExC,GAGA,EAAO,WAAW,CAAC,MAAO,IACpB,EAAa,OAAO,EAAE,AACxB,MAAM,EAAa,OAAO,CAAC,WAAW,CAAC,EAAY,EAEvD,GAEA,GAAI,CAEF,IAAM,EAAQ,MAAM,EAAO,WAAW,GAElC,EAAa,OAAO,EAAE,AACxB,MAAM,EAAa,OAAO,CAAC,WAAW,CAAC,EAAY,CACjD,KAAM,QACN,KAAM,CACR,EAEJ,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,0BAA2B,GACzC,EAAkB,OAAO,CAAC,MAAM,CAAC,GACjC,EAAqB,OAAO,CAAC,MAAM,CAAC,GACpC,EAAqB,OAAO,CAAC,MAAM,CAAC,EACtC,CACF,EAEM,EAAW,MAAO,EAAY,KAClC,IAAM,EAAa,IAEnB,GAAI,CAEF,EAAqB,OAAO,CAAC,GAAG,CAAC,EAAa,EAAE,CAAE,MAAE,aAAM,CAAW,GAGjE,EAAa,OAAO,EAAE,AACxB,MAAM,EAAa,OAAO,CAAC,WAAW,CAAC,EAAa,EAAE,CAAE,CACtD,KAAM,mBACN,KAAM,YACJ,EACA,SAAU,EAAK,IAAI,CACnB,SAAU,EAAK,IAAI,CACnB,SAAU,EAAK,IACjB,AADqB,CAEvB,GAGF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,EAAa,EAAE,CAAA,CAAE,CAC1E,CAAE,MAAO,EAAO,CAId,MAHA,QAAQ,KAAK,CAAC,uBAAwB,GAEtC,EAAqB,OAAO,CAAC,MAAM,CAAC,EAAa,EAAE,EAC7C,CACR,CACF,EAEM,EAAiB,MAAO,IAC5B,IAAM,EAAU,EAAiB,IAAI,CAAC,AAAC,GAAM,EAAE,EAAE,GAAK,GACjD,GAAY,EAAa,MAAd,CAAqB,EAAE,CAGvC,MAAM,EAAa,OAAO,CAAC,WAAW,CAAC,EAAQ,QAAQ,CAAE,CACvD,KAAM,oBACN,KAAM,CACJ,WAAY,EACZ,UAAU,CACZ,CACF,GAGA,EAAoB,AAAC,GAAS,EAAK,MAAM,CAAE,AAAD,GAAO,EAAE,EAAE,GAAK,IAC5D,EAEM,EAAiB,MAAO,IAC5B,IAAM,EAAU,EAAiB,IAAI,CAAC,AAAC,GAAM,EAAE,EAAE,GAAK,GACjD,GAAY,EAAa,MAAd,CAAqB,EAAE,CAGvC,MAAM,EAAa,OAAO,CAAC,WAAW,CAAC,EAAQ,QAAQ,CAAE,CACvD,KAAM,oBACN,KAAM,CACJ,WAAY,EACZ,UAAU,CACZ,CACF,GAGA,EAAoB,AAAC,GAAS,EAAK,MAAM,CAAE,AAAD,GAAO,EAAE,EAAE,GAAK,IAC5D,EAEA,MAAO,SACL,mBACA,kBACA,EACA,cACA,SAAU,EAAY,OAAO,UAC7B,iBACA,iBACA,CACF,CACF,EfxUkB,GAEV,eACJ,CAAa,oBACb,CAAkB,SAClB,CAAO,CACP,MAAO,CAAS,MAChB,CAAI,CACL,CgBtCI,AhBsCD,SgBtCU,EACd,GAAM,CAAC,EAAe,EAAiB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAiB,EAAE,EAE/D,EAAkB,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,CAClC,EACA,EACA,EACA,KAEA,IAAM,EAAK,IACL,EAA6B,IACjC,OACA,QACA,UACA,WACA,CACF,EAGA,OADA,EAAiB,GAAQ,IAAI,EAAM,EAAa,EACzC,CACT,EAAG,EAAE,EAEC,EAAqB,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,AAAC,IACtC,EAAiB,GAAQ,EAAK,MAAM,CAAC,GAAK,EAAE,EAAE,GAAK,GACrD,EAAG,EAAE,EAEC,EAAU,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,CAAC,EAAe,EAAiB,IACpD,EAAgB,UAAW,EAAO,EAAS,GACjD,CAAC,EAAgB,EAEd,EAAQ,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,CAAC,EAAe,EAAiB,IAClD,EAAgB,QAAS,EAAO,EAAS,GAC/C,CAAC,EAAgB,EAEd,EAAO,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,CAAC,EAAe,EAAiB,IACjD,EAAgB,OAAQ,EAAO,EAAS,GAC9C,CAAC,EAAgB,EAEd,EAAU,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,CAAC,EAAe,EAAiB,IACpD,EAAgB,UAAW,EAAO,EAAS,GACjD,CAAC,EAAgB,EAMpB,MAAO,eACL,kBACA,qBACA,UACA,QACA,OACA,UACA,EACA,MAZY,CAAA,EAAA,EAAA,WAAW,AAAX,EAAY,KACxB,EAAiB,EAAE,CACrB,EAAG,EAAE,CAWL,CACF,IhBZQ,EAAqB,MAAO,IAChC,GAAI,EACF,GAAI,CACF,MAAM,EAFQ,AAEC,EAAc,GAC7B,EACE,wBACA,CAAC,QAAQ,EAAE,EAAa,IAAI,CAAC,IAAI,EAAE,EAAO,IAAI,CAAC,yBAAyB,CAAC,EAE3E,EAAgB,KAClB,CAAE,CADuB,KAChB,EAAK,CACZ,QAAQ,KAAK,CAAC,UAFyC,aAEjB,GACtC,EACE,kBACA,CAAC,eAAe,EAAE,EAAa,IAAI,CAAC,IAAI,EAAE,EAAO,IAAI,CAAC,mBAAmB,CAAC,CAE9E,CAEJ,EAEM,EAAuB,MAAO,IAClC,GAAI,CACF,MAAM,EAAe,GACrB,EACE,oBACA,0DAEJ,CAAE,MAAO,EAAK,CACZ,QAAQ,KAAK,CAAC,6BAA8B,GAC5C,EACE,gBACA,wDAEJ,CACF,EAEM,EAAuB,MAAO,IAClC,GAAI,CACF,MAAM,EAAe,GACrB,EAAK,oBAAqB,+CAC5B,CAAE,MAAO,EAAK,CACZ,QAAQ,KAAK,CAAC,6BAA8B,GAC5C,EACE,gBACA,wDAEJ,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0GACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,cAAe,EACf,QAAS,IAEX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,UAAU,6BAChB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,iEAAwD,aAGtE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4CAAmC,8CAGhD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAC,qBAAqB,EAC/B,EAAc,eAAiB,aAAA,CAC/B,GAEJ,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oDACb,EAAc,YAAc,qBAGjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qDAA2C,WAC/C,KAEX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qDAA2C,OACnD,EAAS,KAAK,CAAC,EAAG,GAAG,eAKhC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oEAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,aA1Fa,AAAC,CA0FA,GAzFxB,EAAgB,EAClB,EAyFY,aAAc,IAEhB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,QAAS,EACT,eAAgB,EAChB,aAAc,OAKlB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACZ,EAAiB,MAAM,CAAG,GACzB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+DAAsD,sBAGnE,EAAiB,GAAG,CAAC,AAAC,GACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAEC,QAAS,EACT,SAAU,EACV,SAAU,GAHL,EAAQ,EAAE,MAStB,EAAgB,MAAM,CAAG,GACxB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+DAAsD,qBAGnE,EAAgB,GAAG,CAAE,AAAD,GACnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAEC,SAAU,GADL,EAAS,UAAU,MAOH,IAA5B,EAAiB,MAAM,EAAqC,IAA3B,EAAgB,MAAM,EACtD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAU,oBACV,KAAK,OACL,OAAO,eACP,QAAQ,qBAER,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,sKAIR,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,4CAAmC,kDAUhE", "ignoreList": [6, 7, 8, 9, 10, 11]}