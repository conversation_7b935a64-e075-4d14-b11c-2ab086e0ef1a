import type { VersionInfo } from '../../server/dev/parse-version-info';
import type { SupportedErrorEvent } from './container/runtime-error/render-error';
import type { DebugInfo } from '../shared/types';
import type { DevIndicatorServerState } from '../../server/dev/dev-indicator-server-state';
export type DevToolsConfig = {
    theme?: 'light' | 'dark' | 'system';
    disableDevIndicator?: boolean;
    devToolsPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    devToolsPanelPosition?: Record<string, 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'>;
    devToolsPanelSize?: Record<string, {
        width: number;
        height: number;
    }>;
    scale?: number;
    hideShortcut?: string | null;
};
export type Corners = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
export type DevToolsIndicatorPosition = Corners;
export declare const NEXT_DEV_TOOLS_SCALE: {
    Small: number;
    Medium: number;
    Large: number;
};
export type DevToolsScale = (typeof NEXT_DEV_TOOLS_SCALE)[keyof typeof NEXT_DEV_TOOLS_SCALE];
type FastRefreshState = 
/** No refresh in progress. */
{
    type: 'idle';
}
/** The refresh process has been triggered, but the new code has not been executed yet. */
 | {
    type: 'pending';
    errors: readonly SupportedErrorEvent[];
};
export interface OverlayState {
    readonly nextId: number;
    readonly buildError: string | null;
    readonly errors: readonly SupportedErrorEvent[];
    readonly refreshState: FastRefreshState;
    readonly versionInfo: VersionInfo;
    readonly notFound: boolean;
    readonly buildingIndicator: boolean;
    readonly renderingIndicator: boolean;
    readonly staticIndicator: boolean;
    readonly showIndicator: boolean;
    readonly disableDevIndicator: boolean;
    readonly debugInfo: DebugInfo;
    readonly routerType: 'pages' | 'app';
    /** This flag is used to handle the Error Overlay state in the "old" overlay.
     *  In the DevTools panel, this value will used for the "Error Overlay Mode"
     *  which is viewing the "Issues Tab" as a fullscreen.
     */
    readonly isErrorOverlayOpen: boolean;
    readonly devToolsPosition: Corners;
    readonly devToolsPanelPosition: Readonly<Record<DevtoolsPanelName, Corners>>;
    readonly devToolsPanelSize: Readonly<Record<DevtoolsPanelName, {
        width: number;
        height: number;
    }>>;
    readonly scale: number;
    readonly page: string;
    readonly theme: 'light' | 'dark' | 'system';
    readonly hideShortcut: string | null;
}
type DevtoolsPanelName = string;
export type OverlayDispatch = React.Dispatch<DispatcherEvent>;
export declare const ACTION_STATIC_INDICATOR = "static-indicator";
export declare const ACTION_BUILD_OK = "build-ok";
export declare const ACTION_BUILD_ERROR = "build-error";
export declare const ACTION_BEFORE_REFRESH = "before-fast-refresh";
export declare const ACTION_REFRESH = "fast-refresh";
export declare const ACTION_VERSION_INFO = "version-info";
export declare const ACTION_UNHANDLED_ERROR = "unhandled-error";
export declare const ACTION_UNHANDLED_REJECTION = "unhandled-rejection";
export declare const ACTION_DEBUG_INFO = "debug-info";
export declare const ACTION_DEV_INDICATOR = "dev-indicator";
export declare const ACTION_DEV_INDICATOR_SET = "dev-indicator-disable";
export declare const ACTION_ERROR_OVERLAY_OPEN = "error-overlay-open";
export declare const ACTION_ERROR_OVERLAY_CLOSE = "error-overlay-close";
export declare const ACTION_ERROR_OVERLAY_TOGGLE = "error-overlay-toggle";
export declare const ACTION_BUILDING_INDICATOR_SHOW = "building-indicator-show";
export declare const ACTION_BUILDING_INDICATOR_HIDE = "building-indicator-hide";
export declare const ACTION_RENDERING_INDICATOR_SHOW = "rendering-indicator-show";
export declare const ACTION_RENDERING_INDICATOR_HIDE = "rendering-indicator-hide";
export declare const ACTION_DEVTOOLS_POSITION = "devtools-position";
export declare const ACTION_DEVTOOLS_PANEL_POSITION = "devtools-panel-position";
export declare const ACTION_DEVTOOLS_SCALE = "devtools-scale";
export declare const ACTION_DEVTOOLS_CONFIG = "devtools-config";
export declare const STORAGE_KEY_PANEL_POSITION_PREFIX = "__nextjs-dev-tools-panel-position";
export declare const STORE_KEY_PANEL_SIZE_PREFIX = "__nextjs-dev-tools-panel-size";
export declare const STORE_KEY_SHARED_PANEL_SIZE = "__nextjs-dev-tools-shared-panel-size";
export declare const STORE_KEY_SHARED_PANEL_LOCATION = "__nextjs-dev-tools-shared-panel-location";
export declare const ACTION_DEVTOOL_UPDATE_ROUTE_STATE = "segment-explorer-update-route-state";
interface StaticIndicatorAction {
    type: typeof ACTION_STATIC_INDICATOR;
    staticIndicator: boolean;
}
interface BuildOkAction {
    type: typeof ACTION_BUILD_OK;
}
interface BuildErrorAction {
    type: typeof ACTION_BUILD_ERROR;
    message: string;
}
interface BeforeFastRefreshAction {
    type: typeof ACTION_BEFORE_REFRESH;
}
interface FastRefreshAction {
    type: typeof ACTION_REFRESH;
}
interface UnhandledErrorAction {
    type: typeof ACTION_UNHANDLED_ERROR;
    reason: Error;
}
interface UnhandledRejectionAction {
    type: typeof ACTION_UNHANDLED_REJECTION;
    reason: Error;
}
interface DebugInfoAction {
    type: typeof ACTION_DEBUG_INFO;
    debugInfo: any;
}
interface VersionInfoAction {
    type: typeof ACTION_VERSION_INFO;
    versionInfo: VersionInfo;
}
interface DevIndicatorAction {
    type: typeof ACTION_DEV_INDICATOR;
    devIndicator: DevIndicatorServerState;
}
interface DevIndicatorSetAction {
    type: typeof ACTION_DEV_INDICATOR_SET;
    disabled: boolean;
}
interface ErrorOverlayOpenAction {
    type: typeof ACTION_ERROR_OVERLAY_OPEN;
}
interface ErrorOverlayCloseAction {
    type: typeof ACTION_ERROR_OVERLAY_CLOSE;
}
interface ErrorOverlayToggleAction {
    type: typeof ACTION_ERROR_OVERLAY_TOGGLE;
}
interface BuildingIndicatorShowAction {
    type: typeof ACTION_BUILDING_INDICATOR_SHOW;
}
interface BuildingIndicatorHideAction {
    type: typeof ACTION_BUILDING_INDICATOR_HIDE;
}
interface RenderingIndicatorShowAction {
    type: typeof ACTION_RENDERING_INDICATOR_SHOW;
}
interface RenderingIndicatorHideAction {
    type: typeof ACTION_RENDERING_INDICATOR_HIDE;
}
interface DevToolsIndicatorPositionAction {
    type: typeof ACTION_DEVTOOLS_POSITION;
    devToolsPosition: Corners;
}
interface DevToolsPanelPositionAction {
    type: typeof ACTION_DEVTOOLS_PANEL_POSITION;
    key: string;
    devToolsPanelPosition: Corners;
}
interface DevToolsScaleAction {
    type: typeof ACTION_DEVTOOLS_SCALE;
    scale: number;
}
interface DevToolUpdateRouteStateAction {
    type: typeof ACTION_DEVTOOL_UPDATE_ROUTE_STATE;
    page: string;
}
interface DevToolsConfigAction {
    type: typeof ACTION_DEVTOOLS_CONFIG;
    devToolsConfig: DevToolsConfig;
}
export type DispatcherEvent = BuildOkAction | BuildErrorAction | BeforeFastRefreshAction | FastRefreshAction | UnhandledErrorAction | UnhandledRejectionAction | VersionInfoAction | StaticIndicatorAction | DebugInfoAction | DevIndicatorAction | ErrorOverlayOpenAction | ErrorOverlayCloseAction | ErrorOverlayToggleAction | BuildingIndicatorShowAction | BuildingIndicatorHideAction | RenderingIndicatorShowAction | RenderingIndicatorHideAction | DevToolsIndicatorPositionAction | DevToolsPanelPositionAction | DevToolsScaleAction | DevToolUpdateRouteStateAction | DevIndicatorSetAction | DevToolsConfigAction;
export declare const INITIAL_OVERLAY_STATE: Omit<OverlayState, 'isErrorOverlayOpen' | 'routerType'>;
export declare function useErrorOverlayReducer(routerType: 'pages' | 'app', getOwnerStack: (error: Error) => string | null | undefined, isRecoverableError: (error: Error) => boolean): [OverlayState & {
    routerType: "pages" | "app";
}, import("react").ActionDispatch<[action: DispatcherEvent]>];
export {};
