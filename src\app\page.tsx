"use client";

import { useState, useEffect } from "react";
import FilePicker from "@/components/FilePicker";
import DeviceList from "@/components/DeviceList";
import TransferProgress from "@/components/TransferProgress";
import TransferRequest from "@/components/TransferRequest";
import { NotificationContainer } from "@/components/Notification";
import { useShareWeb } from "@/hooks/useShareWeb";
import { useNotifications } from "@/hooks/useNotifications";
import { Device } from "@/types";

export default function Home() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [deviceName, setDeviceName] = useState<string>("");

  useEffect(() => {
    // Generate a random device name on first load
    const names = [
      "iPhone",
      "MacBook",
      "iPad",
      "Android",
      "Windows PC",
      "Laptop",
    ];
    const randomName = `${
      names[Math.floor(Math.random() * names.length)]
    } ${Math.floor(Math.random() * 100)}`;
    setDeviceName(randomName);
  }, []);

  const {
    devices,
    transferRequests,
    activeTransfers,
    isConnected,
    deviceId,
    sendFile,
    acceptTransfer,
    rejectTransfer,
  } = useShareWeb(deviceName);

  const {
    notifications,
    removeNotification,
    success,
    error: showError,
    info,
  } = useNotifications();

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleDeviceSelect = async (device: Device) => {
    if (selectedFile) {
      try {
        await sendFile(selectedFile, device);
        success(
          "Transfer Request Sent",
          `Sending ${selectedFile.name} to ${device.name}. Waiting for acceptance.`
        );
        setSelectedFile(null); // Clear selection after sending
      } catch (err) {
        console.error("Failed to send file:", err);
        showError(
          "Transfer Failed",
          `Failed to send ${selectedFile.name} to ${device.name}. Please try again.`
        );
      }
    }
  };

  const handleTransferAccept = async (requestId: string) => {
    try {
      await acceptTransfer(requestId);
      success(
        "Transfer Accepted",
        "File transfer has been accepted and will begin shortly."
      );
    } catch (err) {
      console.error("Failed to accept transfer:", err);
      showError(
        "Accept Failed",
        "Failed to accept the file transfer. Please try again."
      );
    }
  };

  const handleTransferReject = async (requestId: string) => {
    try {
      await rejectTransfer(requestId);
      info("Transfer Rejected", "The file transfer request has been declined.");
    } catch (err) {
      console.error("Failed to reject transfer:", err);
      showError(
        "Reject Failed",
        "Failed to reject the file transfer. Please try again."
      );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <NotificationContainer
        notifications={notifications}
        onClose={removeNotification}
      />
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            ShareWeb
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Share files instantly with nearby devices
          </p>
          <div className="flex items-center justify-center space-x-4 mt-4">
            <div className="flex items-center space-x-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  isConnected ? "bg-green-500" : "bg-red-500"
                }`}
              ></div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {isConnected ? "Connected" : "Connecting..."}
              </span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Device: {deviceName}
            </div>
            <div className="text-xs text-gray-400 dark:text-gray-500">
              ID: {deviceId.slice(0, 8)}...
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Left Column - File Selection and Device Discovery */}
          <div className="space-y-6">
            <FilePicker
              onFileSelect={handleFileSelect}
              selectedFile={selectedFile}
            />
            <DeviceList
              devices={devices}
              onDeviceSelect={handleDeviceSelect}
              selectedFile={selectedFile}
            />
          </div>

          {/* Right Column - Transfer Status */}
          <div className="space-y-6">
            {transferRequests.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Incoming Requests
                </h3>
                {transferRequests.map((request) => (
                  <TransferRequest
                    key={request.id}
                    request={request}
                    onAccept={handleTransferAccept}
                    onReject={handleTransferReject}
                  />
                ))}
              </div>
            )}

            {activeTransfers.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Active Transfers
                </h3>
                {activeTransfers.map((transfer) => (
                  <TransferProgress
                    key={transfer.transferId}
                    progress={transfer}
                  />
                ))}
              </div>
            )}

            {transferRequests.length === 0 && activeTransfers.length === 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
                <div className="text-gray-400 dark:text-gray-500 mb-4">
                  <svg
                    className="w-16 h-16 mx-auto"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                </div>
                <p className="text-gray-500 dark:text-gray-400">
                  No active transfers or requests
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
