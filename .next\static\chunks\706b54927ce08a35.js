(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,52683,e=>{"use strict";let t;e.s(["default",()=>v],52683);var n=e.i(43476),r=e.i(71645);function s(e){let{onFileSelect:t,selectedFile:s}=e,a=(0,r.useRef)(null);return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Select File to Share"}),(0,n.jsx)("input",{ref:a,type:"file",onChange:e=>{var n;let r=null==(n=e.target.files)?void 0:n[0];r&&t(r)},className:"hidden",accept:"*/*"}),(0,n.jsx)("div",{onClick:()=>{var e;null==(e=a.current)||e.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-colors",children:s?(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("div",{className:"text-green-600 dark:text-green-400",children:(0,n.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-lg font-medium text-gray-900 dark:text-white",children:s.name}),(0,n.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[(e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(s.size)," • ",s.type||"Unknown type"]})]}),(0,n.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:"Click to select a different file"})]}):(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("div",{className:"text-gray-400 dark:text-gray-500",children:(0,n.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Choose a file to share"}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Click here or drag and drop any file"})]})]})}),s&&(0,n.jsx)("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,n.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[(0,n.jsx)("span",{className:"font-medium",children:"Ready to share!"})," Select a device from the list below to send this file."]})})]})}function a(e){let{devices:t,onDeviceSelect:r,selectedFile:s}=e;return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Nearby Devices"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,n.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Scanning..."})]})]}),0===t.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,n.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"})})}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-2",children:"No devices found"}),(0,n.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-500",children:"Make sure other devices are on the same Wi-Fi network and have ShareWeb open"})]}):(0,n.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,n.jsxs)("div",{onClick:()=>s&&r(e),className:"flex items-center justify-between p-4 rounded-lg border transition-all ".concat(s?"cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-300 dark:hover:border-blue-600 border-gray-200 dark:border-gray-600":"cursor-not-allowed opacity-50 border-gray-200 dark:border-gray-600"),children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"".concat(e.isOnline?"text-green-600 dark:text-green-400":"text-gray-400 dark:text-gray-500"),children:(e=>{let t=e.toLowerCase();return t.includes("iphone")||t.includes("android")?(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"})}):t.includes("ipad")||t.includes("tablet")?(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"})}):(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})(e.name)}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(e=>{let t=Math.floor((Date.now()-e)/1e3),n=Math.floor(t/60),r=Math.floor(n/60);return t<60?"Just now":n<60?"".concat(n,"m ago"):r<24?"".concat(r,"h ago"):"Offline"})(e.lastSeen)})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.isOnline?"bg-green-500":"bg-gray-400")}),s&&(0,n.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]},e.id))}),!s&&t.length>0&&(0,n.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,n.jsxs)("p",{className:"text-sm text-yellow-800 dark:text-yellow-200",children:[(0,n.jsx)("span",{className:"font-medium",children:"Select a file first"})," to send it to one of these devices."]})})]})}function i(e){let{progress:t}=e,r=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-200 dark:border-gray-700",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:(e=>{switch(e){case"preparing":case"transferring":return"text-blue-600 dark:text-blue-400";case"completed":return"text-green-600 dark:text-green-400";case"failed":return"text-red-600 dark:text-red-400";default:return"text-gray-600 dark:text-gray-400"}})(t.status),children:(e=>{switch(e){case"preparing":return(0,n.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})});case"transferring":return(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})});case"completed":return(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})});case"failed":return(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"cancelled":return(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"})});default:return null}})(t.status)}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{className:"font-medium text-gray-900 dark:text-white",children:["Transfer ",t.transferId.slice(0,8),"..."]}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 capitalize",children:t.status})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[t.percentage.toFixed(1),"%"]}),(0,n.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[r(t.bytesTransferred)," / ",r(t.totalBytes)]})]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2",children:(0,n.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat((e=>{switch(e){case"preparing":case"transferring":return"bg-blue-500";case"completed":return"bg-green-500";case"failed":return"bg-red-500";default:return"bg-gray-500"}})(t.status)),style:{width:"".concat(Math.min(t.percentage,100),"%")}})}),"transferring"===t.status&&(0,n.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,n.jsx)("span",{children:"Transferring..."}),(0,n.jsx)("span",{children:"P2P Connection"})]}),"completed"===t.status&&(0,n.jsxs)("div",{className:"flex justify-between text-xs text-green-600 dark:text-green-400",children:[(0,n.jsx)("span",{children:"Transfer completed successfully"}),(0,n.jsx)("span",{children:"✓ Verified"})]}),"failed"===t.status&&(0,n.jsxs)("div",{className:"flex justify-between text-xs text-red-600 dark:text-red-400",children:[(0,n.jsx)("span",{children:"Transfer failed"}),(0,n.jsx)("span",{children:"Connection lost"})]})]})}function o(e){var t;let{request:r,onAccept:s,onReject:a}=e;return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border-l-4 border-blue-500 animate-pulse-slow",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(t=r.fileType).startsWith("image/")?(0,n.jsx)("svg",{className:"w-8 h-8 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}):t.startsWith("video/")?(0,n.jsx)("svg",{className:"w-8 h-8 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"})}):t.startsWith("audio/")?(0,n.jsx)("svg",{className:"w-8 h-8 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"})}):t.includes("pdf")?(0,n.jsx)("svg",{className:"w-8 h-8 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})}):(0,n.jsx)("svg",{className:"w-8 h-8 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["Incoming file from ",(0,n.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:r.senderName})]}),(0,n.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:(e=>{let t=Math.floor((Date.now()-e)/1e3),n=Math.floor(t/60);return t<60?"Just now":n<60?"".concat(n,"m ago"):"A while ago"})(r.timestamp)})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),(0,n.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium",children:"Pending"})]})]}),(0,n.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4",children:(0,n.jsx)("div",{className:"flex items-center justify-between",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-gray-900 dark:text-white truncate",children:r.fileName}),(0,n.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[(e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]})(r.fileSize)," • ",r.fileType||"Unknown type"]})]})})}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Do you want to receive this file?"}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsx)("button",{onClick:()=>a(r.id),className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors",children:"Decline"}),(0,n.jsxs)("button",{onClick:()=>s(r.id),className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center space-x-2",children:[(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,n.jsx)("span",{children:"Accept"})]})]})]})]})}function l(e){let{id:t,type:s,title:a,message:i,duration:o=5e3,onClose:l}=e,[c,d]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=setTimeout(()=>{d(!1),setTimeout(()=>l(t),300)},o);return()=>clearTimeout(e)},[t,o,l]);let h=()=>{switch(s){case"success":return"text-green-800 dark:text-green-200";case"error":return"text-red-800 dark:text-red-200";case"warning":return"text-yellow-800 dark:text-yellow-200";default:return"text-blue-800 dark:text-blue-200"}};return(0,n.jsx)("div",{className:"fixed top-4 right-4 max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 z-50 ".concat(c?"translate-x-0 opacity-100":"translate-x-full opacity-0"," ").concat((()=>{switch(s){case"success":return"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800";case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"}})()),children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(s){case"success":return(0,n.jsx)("svg",{className:"w-5 h-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})});case"error":return(0,n.jsx)("svg",{className:"w-5 h-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"warning":return(0,n.jsx)("svg",{className:"w-5 h-5 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})});default:return(0,n.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}})()}),(0,n.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium ".concat(h()),children:a}),(0,n.jsx)("p",{className:"mt-1 text-sm ".concat(h()," opacity-90"),children:i})]}),(0,n.jsx)("div",{className:"ml-4 flex-shrink-0 flex",children:(0,n.jsx)("button",{onClick:()=>{d(!1),setTimeout(()=>l(t),300)},className:"inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ".concat(h()," hover:opacity-75"),children:(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})})]})})}function c(e){let{notifications:t,onClose:r}=e;return(0,n.jsx)("div",{className:"fixed top-0 right-0 z-50 p-4 space-y-4",children:t.map((e,t)=>(0,n.jsx)("div",{style:{top:"".concat(80*t,"px")},className:"relative",children:(0,n.jsx)(l,{...e,onClose:r})},e.id))})}let d={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},h=new Uint8Array(16),u=[];for(let e=0;e<256;++e)u.push((e+256).toString(16).slice(1));let g=function(e,n,r){var s,a,i;if(d.randomUUID&&!n&&!e)return d.randomUUID();let o=null!=(i=null!=(a=(e=e||{}).random)?a:null==(s=e.rng)?void 0:s.call(e))?i:function(){if(!t){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");t=crypto.getRandomValues.bind(crypto)}return t(h)}();if(o.length<16)throw Error("Random bytes length must be >= 16");if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,n){if((r=r||0)<0||r+16>n.length)throw RangeError("UUID byte range ".concat(r,":").concat(r+15," is out of buffer bounds"));for(let e=0;e<16;++e)n[r+e]=o[e];return n}return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(u[e[t+0]]+u[e[t+1]]+u[e[t+2]]+u[e[t+3]]+"-"+u[e[t+4]]+u[e[t+5]]+"-"+u[e[t+6]]+u[e[t+7]]+"-"+u[e[t+8]]+u[e[t+9]]+"-"+u[e[t+10]]+u[e[t+11]]+u[e[t+12]]+u[e[t+13]]+u[e[t+14]]+u[e[t+15]]).toLowerCase()}(o)};function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class m{async start(){try{await this.register(),this.startPolling(),this.startHeartbeat(),this.startDeviceDiscovery(),console.log("Signaling client started")}catch(e){throw console.error("Failed to start signaling client:",e),e}}async stop(){this.isPolling=!1,this.pollingInterval&&(clearInterval(this.pollingInterval),this.pollingInterval=null),this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null);try{await fetch("".concat(this.baseUrl,"?deviceId=").concat(this.deviceId),{method:"DELETE"})}catch(e){console.error("Failed to unregister device:",e)}console.log("Signaling client stopped")}async register(){if(!(await fetch(this.baseUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",deviceId:this.deviceId,deviceName:this.deviceName})})).ok)throw Error("Failed to register device")}startPolling(){this.isPolling=!0,this.poll()}async poll(){if(this.isPolling){try{let e=await fetch("".concat(this.baseUrl,"?deviceId=").concat(this.deviceId,"&action=poll"),{method:"GET"});if(e.ok){let t=await e.json();t.messages&&t.messages.length>0&&t.messages.forEach(e=>{this.onMessage&&this.onMessage(e)})}}catch(e){console.error("Polling error:",e)}this.isPolling&&setTimeout(()=>this.poll(),1e3)}}startHeartbeat(){this.heartbeatInterval=setInterval(async()=>{try{await fetch(this.baseUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"heartbeat",deviceId:this.deviceId})})}catch(e){console.error("Heartbeat error:",e)}},3e4)}startDeviceDiscovery(){let e=async()=>{try{let e=await fetch("".concat(this.baseUrl,"?deviceId=").concat(this.deviceId,"&action=devices"),{method:"GET"});if(e.ok){let t=await e.json();this.onDevicesUpdate&&t.devices&&this.onDevicesUpdate(t.devices)}}catch(e){console.error("Device discovery error:",e)}};e(),setInterval(e,5e3)}async sendMessage(e,t){try{if(!(await fetch(this.baseUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"send",deviceId:this.deviceId,targetId:e,message:{...t,from:this.deviceId,timestamp:Date.now()}})})).ok)throw Error("Failed to send message")}catch(e){throw console.error("Failed to send message:",e),e}}onDevices(e){this.onDevicesUpdate=e}onSignalingMessage(e){this.onMessage=e}getDeviceId(){return this.deviceId}getDeviceName(){return this.deviceName}constructor(e,t,n="/api/signaling"){x(this,"deviceId",void 0),x(this,"deviceName",void 0),x(this,"baseUrl",void 0),x(this,"pollingInterval",null),x(this,"heartbeatInterval",null),x(this,"onDevicesUpdate",null),x(this,"onMessage",null),x(this,"isPolling",!1),this.deviceId=e,this.deviceName=t,this.baseUrl=n}}let f={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun.l.google.com:5349"},{urls:"stun:stun1.l.google.com:3478"},{urls:"stun:stun1.l.google.com:5349"},{urls:"stun:stun2.l.google.com:19302"},{urls:"stun:stun2.l.google.com:5349"},{urls:"stun:stun3.l.google.com:3478"},{urls:"stun:stun3.l.google.com:5349"},{urls:"stun:stun4.l.google.com:19302"},{urls:"stun:stun4.l.google.com:5349"}],iceCandidatePoolSize:10,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"};class p{setupPeerConnection(){this.peerConnection=new RTCPeerConnection(f),this.peerConnection.onicecandidate=e=>{e.candidate&&this.onSignalingMessage&&this.onSignalingMessage({type:"ice-candidate",from:"",data:e.candidate,timestamp:Date.now()})},this.peerConnection.onicegatheringstatechange=()=>{this.peerConnection&&(this.iceGatheringState=this.peerConnection.iceGatheringState,console.log("ICE gathering state:",this.iceGatheringState))},this.peerConnection.oniceconnectionstatechange=()=>{this.peerConnection&&(console.log("ICE connection state:",this.peerConnection.iceConnectionState),("failed"===this.peerConnection.iceConnectionState||"disconnected"===this.peerConnection.iceConnectionState)&&this.handleConnectionFailure())},this.peerConnection.onconnectionstatechange=()=>{this.peerConnection&&(this.connectionState=this.peerConnection.connectionState,console.log("Connection state:",this.connectionState),this.onConnectionStateChange&&this.onConnectionStateChange(this.connectionState),"connected"===this.connectionState?this.reconnectAttempts=0:("failed"===this.connectionState||"disconnected"===this.connectionState)&&this.handleConnectionFailure())},this.peerConnection.ondatachannel=e=>{let t=e.channel;this.setupDataChannel(t)}}setupDataChannel(e){this.dataChannel=e,e.binaryType="arraybuffer",e.onopen=()=>{console.log("Data channel opened, ready state:",e.readyState),this.onDataChannelOpen&&this.onDataChannelOpen()},e.onmessage=e=>{try{if(this.onDataChannelMessage){let t;if(e.data instanceof ArrayBuffer)t=e.data;else if(e.data instanceof Blob)return void e.data.arrayBuffer().then(e=>{this.onDataChannelMessage&&this.onDataChannelMessage(e)});else return void console.warn("Unexpected data type:",typeof e.data);this.onDataChannelMessage(t)}}catch(e){console.error("Error processing data channel message:",e),this.onError&&this.onError("Failed to process received data")}},e.onclose=()=>{console.log("Data channel closed"),this.onDataChannelClose&&this.onDataChannelClose()},e.onerror=e=>{console.error("Data channel error:",e),this.onError&&this.onError("Data channel error occurred")}}async createOffer(){if(!this.peerConnection)throw Error("Peer connection not initialized");this.isInitiator=!0,this.dataChannel=this.peerConnection.createDataChannel("fileTransfer",{ordered:!0,maxRetransmits:5,maxPacketLifeTime:3e3,protocol:"file-transfer-v1"}),this.setupDataChannel(this.dataChannel);let e=await this.peerConnection.createOffer({offerToReceiveAudio:!1,offerToReceiveVideo:!1});return await this.peerConnection.setLocalDescription(e),e}async createAnswer(e){if(!this.peerConnection)throw Error("Peer connection not initialized");await this.peerConnection.setRemoteDescription(e);let t=await this.peerConnection.createAnswer();return await this.peerConnection.setLocalDescription(t),t}async setRemoteAnswer(e){if(!this.peerConnection)throw Error("Peer connection not initialized");await this.peerConnection.setRemoteDescription(e)}async addIceCandidate(e){if(!this.peerConnection)throw Error("Peer connection not initialized");await this.peerConnection.addIceCandidate(e)}sendData(e){if(!this.dataChannel||"open"!==this.dataChannel.readyState)return!1;try{return this.dataChannel.send(e),!0}catch(e){return console.error("Error sending data:",e),!1}}isDataChannelReady(){var e;return(null==(e=this.dataChannel)?void 0:e.readyState)==="open"}getConnectionState(){var e;return(null==(e=this.peerConnection)?void 0:e.connectionState)||null}handleConnectionFailure(){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.error("Max reconnection attempts reached"),this.onError&&this.onError("Connection failed after multiple attempts");return}this.reconnectAttempts++,console.log("Connection failed, attempting reconnection ".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts)),this.reconnectTimeout&&clearTimeout(this.reconnectTimeout),this.reconnectTimeout=setTimeout(()=>{this.attemptReconnection()},2e3*this.reconnectAttempts)}async attemptReconnection(){try{this.peerConnection&&(this.peerConnection.restartIce(),console.log("ICE restart initiated"))}catch(e){console.error("Failed to restart ICE:",e),this.onError&&this.onError("Failed to restart connection")}}getConnectionInfo(){var e;return this.peerConnection?{connectionState:this.peerConnection.connectionState,iceConnectionState:this.peerConnection.iceConnectionState,iceGatheringState:this.peerConnection.iceGatheringState,signalingState:this.peerConnection.signalingState,dataChannelState:(null==(e=this.dataChannel)?void 0:e.readyState)||"none",reconnectAttempts:this.reconnectAttempts}:null}onSignaling(e){this.onSignalingMessage=e}onDataChannelReady(e){this.onDataChannelOpen=e}onDataReceived(e){this.onDataChannelMessage=e}onDataChannelClosed(e){this.onDataChannelClose=e}onConnectionState(e){this.onConnectionStateChange=e}onErrorOccurred(e){this.onError=e}close(){this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.dataChannel&&(this.dataChannel.close(),this.dataChannel=null),this.peerConnection&&(this.peerConnection.close(),this.peerConnection=null),this.connectionState="new",this.iceGatheringState="new",this.reconnectAttempts=0}constructor(){x(this,"peerConnection",null),x(this,"dataChannel",null),x(this,"onSignalingMessage",null),x(this,"onDataChannelOpen",null),x(this,"onDataChannelMessage",null),x(this,"onDataChannelClose",null),x(this,"onConnectionStateChange",null),x(this,"onError",null),x(this,"connectionState","new"),x(this,"iceGatheringState","new"),x(this,"reconnectAttempts",0),x(this,"maxReconnectAttempts",3),x(this,"reconnectTimeout",null),x(this,"isInitiator",!1),this.setupPeerConnection()}}class y{setupEventHandlers(){this.webrtc.onDataReceived(e=>{this.handleReceivedData(e)}),this.webrtc.onDataChannelReady(()=>{this.sendingFile&&this.startSending()})}async sendFile(e,t){this.sendingFile=e,this.transferId=t,this.currentChunkIndex=0,this.sendingChunks=await this.createFileChunks(e,t),e.name,e.size,e.type,this.sendingChunks.length,this.updateProgress({transferId:t,bytesTransferred:0,totalBytes:e.size,percentage:0,status:"preparing"}),this.webrtc.isDataChannelReady()&&this.startSending()}async createFileChunks(e,t){let n=[],r=Math.ceil(e.size/65536);for(let s=0;s<r;s++){let a=65536*s,i=Math.min(a+65536,e.size),o=e.slice(a,i),l=await o.arrayBuffer();n.push({id:t,index:s,data:l,isLast:s===r-1,totalChunks:r})}return n}startSending(){if(!this.sendingFile||!this.sendingChunks.length)return;let e={fileName:this.sendingFile.name,fileSize:this.sendingFile.size,fileType:this.sendingFile.type,totalChunks:this.sendingChunks.length,transferId:this.transferId};this.webrtc.sendData(new TextEncoder().encode(JSON.stringify({type:"metadata",data:e})).buffer),this.updateProgress({transferId:this.transferId,bytesTransferred:0,totalBytes:this.sendingFile.size,percentage:0,status:"transferring"}),this.sendNextChunk()}sendNextChunk(){var e,t,n,r,s,a;if(this.currentChunkIndex>=this.sendingChunks.length)return void this.updateProgress({transferId:this.transferId,bytesTransferred:(null==(r=this.sendingFile)?void 0:r.size)||0,totalBytes:(null==(s=this.sendingFile)?void 0:s.size)||0,percentage:100,status:"completed"});let i=this.sendingChunks[this.currentChunkIndex],o={type:"chunk-with-data",header:{id:i.id,index:i.index,isLast:i.isLast,totalChunks:i.totalChunks,dataSize:i.data.byteLength},data:Array.from(new Uint8Array(i.data))};if(!this.webrtc.sendData(new TextEncoder().encode(JSON.stringify(o)).buffer)){console.error("Failed to send chunk",i.index),this.updateProgress({transferId:this.transferId,bytesTransferred:0,totalBytes:(null==(a=this.sendingFile)?void 0:a.size)||0,percentage:0,status:"failed"});return}let l=(this.currentChunkIndex+1)*65536,c=Math.min(l/((null==(e=this.sendingFile)?void 0:e.size)||1)*100,100);this.updateProgress({transferId:this.transferId,bytesTransferred:Math.min(l,(null==(t=this.sendingFile)?void 0:t.size)||0),totalBytes:(null==(n=this.sendingFile)?void 0:n.size)||0,percentage:c,status:"transferring"}),this.currentChunkIndex++,setTimeout(()=>this.sendNextChunk(),50)}handleReceivedData(e){try{let t=new TextDecoder().decode(e),n=JSON.parse(t);"metadata"===n.type?this.handleMetadata(n.data):"chunk-with-data"===n.type?this.handleCombinedChunk(n):"chunk"===n.type&&this.handleChunkHeader(n.data)}catch(t){console.error("Error parsing received data:",t),this.handleChunkData(e)}}handleMetadata(e){this.receivingMetadata=e,this.receivedChunks.clear(),this.expectedChunks=e.totalChunks,this.updateProgress({transferId:e.transferId,bytesTransferred:0,totalBytes:e.fileSize,percentage:0,status:"transferring"})}handleChunkHeader(e){this.lastChunkHeader=e}handleChunkData(e){if(!this.lastChunkHeader||!this.receivingMetadata)return;let t=this.lastChunkHeader.index;this.receivedChunks.set(t,e);let n=65536*this.receivedChunks.size,r=Math.min(n/this.receivingMetadata.fileSize*100,100);this.updateProgress({transferId:this.receivingMetadata.transferId,bytesTransferred:Math.min(n,this.receivingMetadata.fileSize),totalBytes:this.receivingMetadata.fileSize,percentage:r,status:"transferring"}),this.receivedChunks.size===this.expectedChunks&&this.assembleFile(),this.lastChunkHeader=null}handleCombinedChunk(e){if(!this.receivingMetadata)return void console.error("Received chunk without metadata");let{header:t,data:n}=e,r=t.index,s=new Uint8Array(n).buffer;if(s.byteLength!==t.dataSize)return void console.error("Chunk ".concat(r," size mismatch: expected ").concat(t.dataSize,", got ").concat(s.byteLength));this.receivedChunks.set(r,s),console.log("Received chunk ".concat(r,"/").concat(t.totalChunks-1));let a=65536*this.receivedChunks.size,i=Math.min(a/this.receivingMetadata.fileSize*100,100);this.updateProgress({transferId:this.receivingMetadata.transferId,bytesTransferred:Math.min(a,this.receivingMetadata.fileSize),totalBytes:this.receivingMetadata.fileSize,percentage:i,status:"transferring"}),this.receivedChunks.size===this.expectedChunks&&(console.log("All chunks received, assembling file..."),this.assembleFile())}assembleFile(){if(!this.receivingMetadata)return;let e=[];for(let t=0;t<this.expectedChunks;t++){let n=this.receivedChunks.get(t);n&&e.push(n)}let t=new Blob(e,{type:this.receivingMetadata.fileType});this.updateProgress({transferId:this.receivingMetadata.transferId,bytesTransferred:this.receivingMetadata.fileSize,totalBytes:this.receivingMetadata.fileSize,percentage:100,status:"completed"}),this.onTransferComplete&&this.onTransferComplete(t,this.receivingMetadata),this.downloadFile(t,this.receivingMetadata.fileName),this.receivingMetadata=null,this.receivedChunks.clear()}downloadFile(e,t){let n=URL.createObjectURL(e),r=document.createElement("a");r.href=n,r.download=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n)}updateProgress(e){this.onProgressUpdate&&this.onProgressUpdate(e)}onProgress(e){this.onProgressUpdate=e}onComplete(e){this.onTransferComplete=e}onError(e){this.onTransferError=e}constructor(e){x(this,"webrtc",void 0),x(this,"onProgressUpdate",null),x(this,"onTransferComplete",null),x(this,"onTransferError",null),x(this,"sendingFile",null),x(this,"sendingChunks",[]),x(this,"currentChunkIndex",0),x(this,"transferId",""),x(this,"receivingMetadata",null),x(this,"receivedChunks",new Map),x(this,"expectedChunks",0),x(this,"lastChunkHeader",null),this.webrtc=e,this.setupEventHandlers()}}function v(){let[e,t]=(0,r.useState)(null),[l,d]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=["iPhone","MacBook","iPad","Android","Windows PC","Laptop"];d("".concat(e[Math.floor(Math.random()*e.length)]," ").concat(Math.floor(100*Math.random())))},[]);let{devices:h,transferRequests:u,activeTransfers:x,isConnected:f,deviceId:v,sendFile:k,acceptTransfer:w,rejectTransfer:C}=function(e){let[t,n]=(0,r.useState)([]),[s,a]=(0,r.useState)([]),[i,o]=(0,r.useState)([]),[l,c]=(0,r.useState)(!1),d=(0,r.useRef)(null),h=(0,r.useRef)(new Map),u=(0,r.useRef)(new Map),x=(0,r.useRef)(g());(0,r.useEffect)(()=>((async()=>{try{let t=new m(x.current,e);d.current=t,t.onDevices(e=>{n(e)}),t.onSignalingMessage(f),await t.start(),c(!0)}catch(e){console.error("Failed to initialize signaling:",e),c(!1)}})(),()=>{d.current&&d.current.stop(),h.current.forEach(e=>e.close()),h.current.clear(),u.current.clear()}),[e]);let f=(0,r.useCallback)(async e=>{let{type:t,from:n,data:r}=e;switch(t){case"transfer-request":v(n,r);break;case"transfer-response":k(n,r);break;case"offer":await w(n,r);break;case"answer":await C(n,r);break;case"ice-candidate":await b(n,r)}},[]),v=(e,n)=>{let r=t.find(t=>t.id===e),s={id:n.transferId,senderId:e,senderName:(null==r?void 0:r.name)||"Unknown Device",fileName:n.fileName,fileSize:n.fileSize,fileType:n.fileType,timestamp:Date.now()};a(e=>[...e,s])},k=async(e,t)=>{t.accepted?await j(e,t.transferId):console.log("Transfer rejected by",e)},w=async(e,t)=>{console.log("Handling offer from",e);let n=new p;h.current.set(e,n),n.onConnectionState(t=>{console.log("Connection state with ".concat(e,":"),t),"connected"===t?c(!0):("failed"===t||"disconnected"===t)&&c(!1)}),n.onErrorOccurred(t=>{console.error("WebRTC error with ".concat(e,":"),t),h.current.delete(e),u.current.delete(e)});let r=new y(n);u.current.set(e,r),r.onProgress(e=>{o(t=>{let n=t.findIndex(t=>t.transferId===e.transferId);if(!(n>=0))return[...t,e];{let r=[...t];return r[n]=e,r}})}),n.onSignaling(async t=>{d.current&&await d.current.sendMessage(e,t)});try{let r=await n.createAnswer(t);d.current&&await d.current.sendMessage(e,{type:"answer",data:r})}catch(t){console.error("Failed to create answer:",t),h.current.delete(e),u.current.delete(e)}},C=async(e,t)=>{let n=h.current.get(e);n&&await n.setRemoteAnswer(t)},b=async(e,t)=>{let n=h.current.get(e);n&&await n.addIceCandidate(t)},j=async(e,t)=>{console.log("Initiating WebRTC connection to",e);let n=new p;h.current.set(e,n),n.onConnectionState(t=>{console.log("Connection state with ".concat(e,":"),t),"connected"===t?c(!0):("failed"===t||"disconnected"===t)&&c(!1)}),n.onErrorOccurred(t=>{console.error("WebRTC error with ".concat(e,":"),t),h.current.delete(e),u.current.delete(e)});let r=new y(n);u.current.set(e,r),r.onProgress(e=>{o(t=>{let n=t.findIndex(t=>t.transferId===e.transferId);if(!(n>=0))return[...t,e];{let r=[...t];return r[n]=e,r}})}),n.onSignaling(async t=>{d.current&&await d.current.sendMessage(e,t)});try{let t=await n.createOffer();d.current&&await d.current.sendMessage(e,{type:"offer",data:t})}catch(t){console.error("Failed to create offer:",t),h.current.delete(e),u.current.delete(e)}},N=async(e,t)=>{let n=g();try{d.current&&await d.current.sendMessage(t.id,{type:"transfer-request",data:{transferId:n,fileName:e.name,fileSize:e.size,fileType:e.type}});let r=u.current.get(t.id);r?await r.sendFile(e,n):console.log("File transfer manager not ready, will send after connection is established")}catch(e){throw console.error("Failed to send file:",e),e}},M=async e=>{let t=s.find(t=>t.id===e);t&&d.current&&(await d.current.sendMessage(t.senderId,{type:"transfer-response",data:{transferId:e,accepted:!0}}),a(t=>t.filter(t=>t.id!==e)))},S=async e=>{let t=s.find(t=>t.id===e);t&&d.current&&(await d.current.sendMessage(t.senderId,{type:"transfer-response",data:{transferId:e,accepted:!1}}),a(t=>t.filter(t=>t.id!==e)))};return{devices:t,transferRequests:s,activeTransfers:i,isConnected:l,deviceId:x.current,sendFile:N,acceptTransfer:M,rejectTransfer:S}}(l),{notifications:b,removeNotification:j,success:N,error:M,info:S}=function(){let[e,t]=(0,r.useState)([]),n=(0,r.useCallback)((e,n,r,s)=>{let a=g(),i={id:a,type:e,title:n,message:r,duration:s};return t(e=>[...e,i]),a},[]),s=(0,r.useCallback)(e=>{t(t=>t.filter(t=>t.id!==e))},[]),a=(0,r.useCallback)((e,t,r)=>n("success",e,t,r),[n]),i=(0,r.useCallback)((e,t,r)=>n("error",e,t,r),[n]),o=(0,r.useCallback)((e,t,r)=>n("info",e,t,r),[n]),l=(0,r.useCallback)((e,t,r)=>n("warning",e,t,r),[n]);return{notifications:e,addNotification:n,removeNotification:s,success:a,error:i,info:o,warning:l,clear:(0,r.useCallback)(()=>{t([])},[])}}(),I=async n=>{if(e)try{await k(e,n),N("Transfer Request Sent","Sending ".concat(e.name," to ").concat(n.name,". Waiting for acceptance.")),t(null)}catch(t){console.error("Failed to send file:",t),M("Transfer Failed","Failed to send ".concat(e.name," to ").concat(n.name,". Please try again."))}},L=async e=>{try{await w(e),N("Transfer Accepted","File transfer has been accepted and will begin shortly.")}catch(e){console.error("Failed to accept transfer:",e),M("Accept Failed","Failed to accept the file transfer. Please try again.")}},T=async e=>{try{await C(e),S("Transfer Rejected","The file transfer request has been declined.")}catch(e){console.error("Failed to reject transfer:",e),M("Reject Failed","Failed to reject the file transfer. Please try again.")}};return(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:[(0,n.jsx)(c,{notifications:b,onClose:j}),(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsxs)("header",{className:"text-center mb-8",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-2",children:"ShareWeb"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Share files instantly with nearby devices"}),(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-4 mt-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(f?"bg-green-500":"bg-red-500")}),(0,n.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:f?"Connected":"Connecting..."})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Device: ",l]}),(0,n.jsxs)("div",{className:"text-xs text-gray-400 dark:text-gray-500",children:["ID: ",v.slice(0,8),"..."]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)(s,{onFileSelect:e=>{t(e)},selectedFile:e}),(0,n.jsx)(a,{devices:h,onDeviceSelect:I,selectedFile:e})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[u.length>0&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Incoming Requests"}),u.map(e=>(0,n.jsx)(o,{request:e,onAccept:L,onReject:T},e.id))]}),x.length>0&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Active Transfers"}),x.map(e=>(0,n.jsx)(i,{progress:e},e.transferId))]}),0===u.length&&0===x.length&&(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-8 text-center",children:[(0,n.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,n.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No active transfers or requests"})]})]})]})]})]})}}]);