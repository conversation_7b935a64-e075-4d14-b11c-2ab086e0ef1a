{"sorted_middleware": ["/api/signaling/route"], "middleware": {}, "instrumentation": null, "functions": {"/api/signaling/route": {"files": ["server/middleware-build-manifest.js", "server/interception-route-rewrite-manifest.js", "server/server-reference-manifest.js", "server/app/api/signaling/route_client-reference-manifest.js", "server/edge/chunks/_next-internal_server_app_api_signaling_route_actions_afdbea25.js", "server/edge/chunks/turbopack-_next-internal_server_app_api_signaling_route_actions_6e6a33c6.js", "server/edge/chunks/_7f82b0d6._.js", "server/edge/chunks/[root-of-the-server]__69987f34._.js", "server/edge/chunks/turbopack-edge-wrapper_a89aeee8.js"], "name": "app/api/signaling/route", "page": "/api/signaling/route", "matchers": [{"regexp": "^/api/signaling(?:/)?$", "originalSource": "/api/signaling"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "l5tUOdkYXwsAMT-5MtYDU", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "h3Znwloe28mLuy45RQxeMt1gOd2eAUcnxwwSWbX7gr4=", "__NEXT_PREVIEW_MODE_ID": "8ce6961e21b4f736f3734e7e69fb3e10", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "edd812f17e71d38cf92bfd3b09a0d5eab5d2a78dc08cc61a0d82dd0b0743f83c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "29faac8f5e204b3ec799c1a11159c8a2a2c6ae708e4d69c1f427a746191298e2"}}}}