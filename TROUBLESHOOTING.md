# ShareWeb Troubleshooting Guide

## Connection Stability Issues

### Symptoms

- Connection status indicator frequently changes between "Connected" and "Connecting..."
- WebRTC connections drop and reconnect repeatedly
- Devices appear and disappear from the device list

### Fixes Implemented

1. **Enhanced WebRTC Configuration**

   - Added multiple STUN servers for better NAT traversal
   - Increased ICE candidate pool size to 10
   - Optimized bundle and RTCP mux policies

2. **Connection State Monitoring**

   - Added comprehensive connection state tracking
   - Implemented automatic reconnection with exponential backoff
   - Added ICE gathering and connection state logging

3. **Error Handling**
   - Added proper error callbacks and logging
   - Implemented connection failure detection
   - Added automatic cleanup of failed connections

### Debugging Steps

1. **Check Browser Console**

   ```javascript
   // Look for these log messages:
   // "ICE gathering state: complete"
   // "Connection state: connected"
   // "Data channel opened, ready state: open"
   ```

2. **Monitor Connection Info**

   ```javascript
   // In browser console, check connection details:
   webrtcManager.getConnectionInfo();
   ```

3. **Network Requirements**
   - Ensure devices are on the same Wi-Fi network
   - Check firewall settings (allow WebRTC traffic)
   - Verify STUN server accessibility

## File Transfer Failures

### Symptoms

- Transfer requests are sent but files don't transfer
- Progress indicators show 0% or get stuck
- "Transfer failed" notifications appear

### Fixes Implemented

1. **Improved File Transfer Protocol**

   - Combined chunk headers and data to eliminate race conditions
   - Added proper error handling and validation
   - Increased chunk transmission delays for mobile compatibility

2. **Mobile Browser Compatibility**

   - Set data channel binary type to 'arraybuffer'
   - Added Blob to ArrayBuffer conversion for mobile browsers
   - Enhanced data type validation and error handling

3. **Better Progress Tracking**
   - Added detailed chunk-level logging
   - Improved progress calculation accuracy
   - Added transfer completion verification

### Debugging Steps

1. **Check Data Channel Status**

   ```javascript
   // In browser console:
   console.log("Data channel ready:", webrtcManager.isDataChannelReady());
   ```

2. **Monitor File Transfer Progress**

   ```javascript
   // Look for these console messages:
   // "Received chunk X/Y"
   // "All chunks received, assembling file..."
   // "Transfer completed successfully"
   ```

3. **Verify File Integrity**
   - Check if received file size matches original
   - Verify file type and content after transfer

### Transfer Accepted But No File Transfer (FIXED)

**Symptoms:**

- Transfer request is sent and accepted
- UI shows transfer as accepted
- No actual file transfer occurs
- No progress updates or file download

**Root Cause:**
File transfer was attempted before WebRTC connection was established, causing silent failure.

**Solution Implemented:**

- Added pending file transfers system
- Files are stored when transfer is requested
- Files are sent only after WebRTC data channel is ready
- Proper cleanup on errors and rejections

**Console Messages to Look For:**

```javascript
// Successful flow:
"Stored pending file transfer for device [id]";
"Data channel ready, sending pending file for device [id]";
"Received chunk 0/X";

// If you see this without the data channel message, the old bug was present:
"File transfer manager not ready, will send after connection is established";
```

## Device Discovery Issues

### Symptoms

- Devices don't appear in the "Nearby Devices" list
- Device list is empty or shows outdated devices
- Intermittent device visibility

### Debugging Steps

1. **Check Signaling Connection**

   ```javascript
   // Verify signaling API is working:
   fetch("/api/signaling?deviceId=test&action=devices")
     .then((r) => r.json())
     .then(console.log);
   ```

2. **Monitor Device Registration**

   ```javascript
   // Look for these console messages:
   // "Signaling client started"
   // "Device registered successfully"
   ```

3. **Network Connectivity**
   - Ensure stable internet connection
   - Check if Vercel Edge Functions are accessible
   - Verify WebSocket/polling connections

## Browser Compatibility Issues

### Supported Browsers

- ✅ Chrome 56+ (Desktop & Mobile)
- ✅ Firefox 51+ (Desktop & Mobile)
- ✅ Safari 11+ (Desktop & Mobile)
- ✅ Edge 79+

### Common Issues by Browser

#### Safari (iOS/macOS)

- **Issue**: Data channel may use Blob instead of ArrayBuffer
- **Fix**: Added automatic Blob to ArrayBuffer conversion
- **Workaround**: Use Chrome or Firefox if issues persist

#### Chrome Mobile (Android)

- **Issue**: Connection drops on screen lock/background
- **Fix**: Added connection monitoring and auto-reconnection
- **Workaround**: Keep app in foreground during transfers

#### Firefox Mobile

- **Issue**: Slower WebRTC connection establishment
- **Fix**: Increased connection timeouts and retry attempts
- **Workaround**: Wait longer for initial connection

## Performance Optimization

### Large File Transfers

1. **Chunk Size**: Optimized to 64KB for best performance
2. **Transmission Rate**: Added 50ms delays between chunks for mobile
3. **Memory Management**: Automatic cleanup after transfer completion

### Network Conditions

1. **Poor Wi-Fi**: Automatic retry with exponential backoff
2. **High Latency**: Increased timeout values for mobile networks
3. **Packet Loss**: Enhanced error detection and recovery

## Advanced Debugging

### Enable Verbose Logging

Add this to browser console for detailed logs:

```javascript
// Enable WebRTC debug logging
localStorage.setItem("debug", "webrtc:*");
```

### Monitor WebRTC Statistics

```javascript
// Get detailed WebRTC stats
webrtcManager.peerConnection.getStats().then((stats) => {
  stats.forEach((report) => {
    if (report.type === "data-channel") {
      console.log("Data channel stats:", report);
    }
  });
});
```

### Check Network Configuration

```javascript
// Test STUN server connectivity
const pc = new RTCPeerConnection({
  iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
});
pc.createDataChannel("test");
pc.createOffer().then((offer) => pc.setLocalDescription(offer));
pc.onicecandidate = (e) => {
  if (e.candidate) {
    console.log("ICE candidate:", e.candidate);
  } else {
    console.log("ICE gathering complete");
  }
};
```

## Common Solutions

### Connection Won't Establish

1. Refresh both devices
2. Check Wi-Fi network (same network required)
3. Disable VPN or proxy
4. Try different browsers
5. Check firewall settings

### File Transfer Stuck

1. Cancel and retry transfer
2. Try smaller file first
3. Check available memory/storage
4. Ensure stable network connection
5. Keep both devices active/unlocked

### Devices Not Visible

1. Refresh the page
2. Check network connectivity
3. Verify signaling server status
4. Try different device names
5. Clear browser cache

## Getting Help

If issues persist:

1. Check browser console for error messages
2. Note device types and browser versions
3. Test with different file sizes
4. Try on different networks
5. Report issues with detailed logs

## Performance Tips

1. **Use Chrome or Firefox** for best compatibility
2. **Keep devices unlocked** during transfers
3. **Use stable Wi-Fi** connection
4. **Transfer smaller files** first to test
5. **Close other apps** to free memory
6. **Stay on same network** throughout transfer
