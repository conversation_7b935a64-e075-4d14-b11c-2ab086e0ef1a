[{"D:\\GitHub\\Test\\share-web\\src\\app\\api\\signaling\\route.ts": "1", "D:\\GitHub\\Test\\share-web\\src\\app\\layout.tsx": "2", "D:\\GitHub\\Test\\share-web\\src\\app\\page.tsx": "3", "D:\\GitHub\\Test\\share-web\\src\\components\\DeviceList.tsx": "4", "D:\\GitHub\\Test\\share-web\\src\\components\\FilePicker.tsx": "5", "D:\\GitHub\\Test\\share-web\\src\\components\\Notification.tsx": "6", "D:\\GitHub\\Test\\share-web\\src\\components\\TransferProgress.tsx": "7", "D:\\GitHub\\Test\\share-web\\src\\components\\TransferRequest.tsx": "8", "D:\\GitHub\\Test\\share-web\\src\\hooks\\useNotifications.ts": "9", "D:\\GitHub\\Test\\share-web\\src\\hooks\\useShareWeb.ts": "10", "D:\\GitHub\\Test\\share-web\\src\\lib\\fileTransfer.ts": "11", "D:\\GitHub\\Test\\share-web\\src\\lib\\signaling.ts": "12", "D:\\GitHub\\Test\\share-web\\src\\lib\\webrtc.ts": "13", "D:\\GitHub\\Test\\share-web\\src\\types\\index.ts": "14"}, {"size": 4105, "mtime": 1755654083112, "results": "15", "hashOfConfig": "16"}, {"size": 689, "mtime": 1755653614509, "results": "17", "hashOfConfig": "16"}, {"size": 6920, "mtime": 1755654284893, "results": "18", "hashOfConfig": "16"}, {"size": 5445, "mtime": 1755653912068, "results": "19", "hashOfConfig": "16"}, {"size": 3481, "mtime": 1755653887259, "results": "20", "hashOfConfig": "16"}, {"size": 5603, "mtime": 1755654296420, "results": "21", "hashOfConfig": "16"}, {"size": 5305, "mtime": 1755653934731, "results": "22", "hashOfConfig": "16"}, {"size": 5599, "mtime": 1755653960091, "results": "23", "hashOfConfig": "16"}, {"size": 1763, "mtime": 1755654208510, "results": "24", "hashOfConfig": "16"}, {"size": 7891, "mtime": 1755654131410, "results": "25", "hashOfConfig": "16"}, {"size": 8856, "mtime": 1755654483083, "results": "26", "hashOfConfig": "16"}, {"size": 5233, "mtime": 1755654103120, "results": "27", "hashOfConfig": "16"}, {"size": 5268, "mtime": 1755654022136, "results": "28", "hashOfConfig": "16"}, {"size": 1090, "mtime": 1755653786920, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "4h3bym", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\GitHub\\Test\\share-web\\src\\app\\api\\signaling\\route.ts", ["72"], [], "D:\\GitHub\\Test\\share-web\\src\\app\\layout.tsx", [], [], "D:\\GitHub\\Test\\share-web\\src\\app\\page.tsx", [], [], "D:\\GitHub\\Test\\share-web\\src\\components\\DeviceList.tsx", [], [], "D:\\GitHub\\Test\\share-web\\src\\components\\FilePicker.tsx", [], [], "D:\\GitHub\\Test\\share-web\\src\\components\\Notification.tsx", [], [], "D:\\GitHub\\Test\\share-web\\src\\components\\TransferProgress.tsx", [], [], "D:\\GitHub\\Test\\share-web\\src\\components\\TransferRequest.tsx", [], [], "D:\\GitHub\\Test\\share-web\\src\\hooks\\useNotifications.ts", [], [], "D:\\GitHub\\Test\\share-web\\src\\hooks\\useShareWeb.ts", ["73", "74", "75", "76", "77", "78", "79"], [], "D:\\GitHub\\Test\\share-web\\src\\lib\\fileTransfer.ts", ["80"], [], "D:\\GitHub\\Test\\share-web\\src\\lib\\signaling.ts", [], [], "D:\\GitHub\\Test\\share-web\\src\\lib\\webrtc.ts", [], [], "D:\\GitHub\\Test\\share-web\\src\\types\\index.ts", ["81"], [], {"ruleId": "82", "severity": 2, "message": "83", "line": 11, "column": 41, "nodeType": "84", "messageId": "85", "endLine": 11, "endColumn": 44, "suggestions": "86"}, {"ruleId": "87", "severity": 1, "message": "88", "line": 50, "column": 25, "nodeType": "89", "endLine": 50, "endColumn": 32}, {"ruleId": "87", "severity": 1, "message": "90", "line": 51, "column": 28, "nodeType": "89", "endLine": 51, "endColumn": 35}, {"ruleId": "87", "severity": 1, "message": "91", "line": 53, "column": 6, "nodeType": "92", "endLine": 53, "endColumn": 18, "suggestions": "93"}, {"ruleId": "87", "severity": 1, "message": "94", "line": 79, "column": 6, "nodeType": "92", "endLine": 79, "endColumn": 8, "suggestions": "95"}, {"ruleId": "82", "severity": 2, "message": "83", "line": 81, "column": 58, "nodeType": "84", "messageId": "85", "endLine": 81, "endColumn": 61, "suggestions": "96"}, {"ruleId": "82", "severity": 2, "message": "83", "line": 96, "column": 67, "nodeType": "84", "messageId": "85", "endLine": 96, "endColumn": 70, "suggestions": "97"}, {"ruleId": "98", "severity": 1, "message": "99", "line": 159, "column": 63, "nodeType": null, "messageId": "100", "endLine": 159, "endColumn": 73}, {"ruleId": "98", "severity": 1, "message": "101", "line": 61, "column": 11, "nodeType": null, "messageId": "100", "endLine": 61, "endColumn": 19}, {"ruleId": "82", "severity": 2, "message": "83", "line": 30, "column": 9, "nodeType": "84", "messageId": "85", "endLine": 30, "endColumn": 12, "suggestions": "102"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["103", "104"], "react-hooks/exhaustive-deps", "The ref value 'webrtcConnections.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'webrtcConnections.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "The ref value 'fileTransferManagers.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'fileTransferManagers.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'handleSignalingMessage'. Either include it or remove the dependency array.", "ArrayExpression", ["105"], "React Hook useCallback has missing dependencies: 'handleTransferRequest' and 'handleTransferResponse'. Either include them or remove the dependency array.", ["106"], ["107", "108"], ["109", "110"], "@typescript-eslint/no-unused-vars", "'transferId' is defined but never used.", "unusedVar", "'metadata' is assigned a value but never used.", ["111", "112"], {"messageId": "113", "fix": "114", "desc": "115"}, {"messageId": "116", "fix": "117", "desc": "118"}, {"desc": "119", "fix": "120"}, {"desc": "121", "fix": "122"}, {"messageId": "113", "fix": "123", "desc": "115"}, {"messageId": "116", "fix": "124", "desc": "118"}, {"messageId": "113", "fix": "125", "desc": "115"}, {"messageId": "116", "fix": "126", "desc": "118"}, {"messageId": "113", "fix": "127", "desc": "115"}, {"messageId": "116", "fix": "128", "desc": "118"}, "suggestUnknown", {"range": "129", "text": "130"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "131", "text": "132"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [deviceName, handleSignalingMessage]", {"range": "133", "text": "134"}, "Update the dependencies array to be: [handleTransferRequest, handleTransferResponse]", {"range": "135", "text": "136"}, {"range": "137", "text": "130"}, {"range": "138", "text": "132"}, {"range": "139", "text": "130"}, {"range": "140", "text": "132"}, {"range": "141", "text": "130"}, {"range": "142", "text": "132"}, [294, 297], "unknown", [294, 297], "never", [1942, 1954], "[deviceName, handleSignalingMessage]", [2563, 2565], "[handleTransferRequest, handleTransferResponse]", [2626, 2629], [2626, 2629], [3098, 3101], [3098, 3101], [680, 683], [680, 683]]