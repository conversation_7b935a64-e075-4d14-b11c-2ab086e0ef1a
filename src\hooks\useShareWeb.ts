import { useState, useEffect, useCallback, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import {
  Device,
  FileTransferRequest,
  FileTransferProgress,
  SignalingMessage,
} from "@/types";
import { SignalingClient } from "@/lib/signaling";
import { WebRTCManager } from "@/lib/webrtc";
import { FileTransferManager } from "@/lib/fileTransfer";

export function useShareWeb(deviceName: string) {
  const [devices, setDevices] = useState<Device[]>([]);
  const [transferRequests, setTransferRequests] = useState<
    FileTransferRequest[]
  >([]);
  const [activeTransfers, setActiveTransfers] = useState<
    FileTransferProgress[]
  >([]);
  const [isConnected, setIsConnected] = useState(false);

  const signalingRef = useRef<SignalingClient | null>(null);
  const webrtcConnections = useRef<Map<string, WebRTCManager>>(new Map());
  const fileTransferManagers = useRef<Map<string, FileTransferManager>>(
    new Map()
  );
  const deviceIdRef = useRef<string>(uuidv4());

  useEffect(() => {
    const initializeSignaling = async () => {
      try {
        const signaling = new SignalingClient(deviceIdRef.current, deviceName);
        signalingRef.current = signaling;

        // Handle device updates
        signaling.onDevices((discoveredDevices) => {
          setDevices(discoveredDevices);
        });

        // Handle signaling messages
        signaling.onSignalingMessage(handleSignalingMessage);

        await signaling.start();
        setIsConnected(true);
      } catch (error) {
        console.error("Failed to initialize signaling:", error);
        setIsConnected(false);
      }
    };

    initializeSignaling();

    return () => {
      if (signalingRef.current) {
        signalingRef.current.stop();
      }

      // Clean up WebRTC connections
      webrtcConnections.current.forEach((connection) => connection.close());
      webrtcConnections.current.clear();
      fileTransferManagers.current.clear();
    };
  }, [deviceName]);

  const handleSignalingMessage = useCallback(
    async (message: SignalingMessage) => {
      const { type, from, data } = message;

      switch (type) {
        case "transfer-request":
          handleTransferRequest(from, data);
          break;

        case "transfer-response":
          handleTransferResponse(from, data);
          break;

        case "offer":
          await handleOffer(from, data);
          break;

        case "answer":
          await handleAnswer(from, data);
          break;

        case "ice-candidate":
          await handleIceCandidate(from, data);
          break;
      }
    },
    []
  );

  const handleTransferRequest = (senderId: string, data: any) => {
    const senderDevice = devices.find((d) => d.id === senderId);
    const request: FileTransferRequest = {
      id: data.transferId,
      senderId,
      senderName: senderDevice?.name || "Unknown Device",
      fileName: data.fileName,
      fileSize: data.fileSize,
      fileType: data.fileType,
      timestamp: Date.now(),
    };

    setTransferRequests((prev) => [...prev, request]);
  };

  const handleTransferResponse = async (receiverId: string, data: any) => {
    if (data.accepted) {
      // Start WebRTC connection
      await initiateWebRTCConnection(receiverId, data.transferId);
    } else {
      // Transfer was rejected
      console.log("Transfer rejected by", receiverId);
    }
  };

  const handleOffer = async (
    senderId: string,
    offer: RTCSessionDescriptionInit
  ) => {
    console.log("Handling offer from", senderId);
    const webrtc = new WebRTCManager();
    webrtcConnections.current.set(senderId, webrtc);

    // Set up connection state monitoring
    webrtc.onConnectionState((state) => {
      console.log(`Connection state with ${senderId}:`, state);
      if (state === "connected") {
        setIsConnected(true);
      } else if (state === "failed" || state === "disconnected") {
        setIsConnected(false);
      }
    });

    // Set up error handling
    webrtc.onErrorOccurred((error) => {
      console.error(`WebRTC error with ${senderId}:`, error);
      // Remove failed connection
      webrtcConnections.current.delete(senderId);
      fileTransferManagers.current.delete(senderId);
    });

    // Set up file transfer manager
    const fileTransfer = new FileTransferManager(webrtc);
    fileTransferManagers.current.set(senderId, fileTransfer);

    fileTransfer.onProgress((progress) => {
      setActiveTransfers((prev) => {
        const index = prev.findIndex(
          (t) => t.transferId === progress.transferId
        );
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = progress;
          return updated;
        } else {
          return [...prev, progress];
        }
      });
    });

    // Set up WebRTC signaling
    webrtc.onSignaling(async (signalingMessage) => {
      if (signalingRef.current) {
        await signalingRef.current.sendMessage(senderId, signalingMessage);
      }
    });

    try {
      // Create answer
      const answer = await webrtc.createAnswer(offer);

      if (signalingRef.current) {
        await signalingRef.current.sendMessage(senderId, {
          type: "answer",
          data: answer,
        });
      }
    } catch (error) {
      console.error("Failed to create answer:", error);
      webrtcConnections.current.delete(senderId);
      fileTransferManagers.current.delete(senderId);
    }
  };

  const handleAnswer = async (
    receiverId: string,
    answer: RTCSessionDescriptionInit
  ) => {
    const webrtc = webrtcConnections.current.get(receiverId);
    if (webrtc) {
      await webrtc.setRemoteAnswer(answer);
    }
  };

  const handleIceCandidate = async (
    peerId: string,
    candidate: RTCIceCandidateInit
  ) => {
    const webrtc = webrtcConnections.current.get(peerId);
    if (webrtc) {
      await webrtc.addIceCandidate(candidate);
    }
  };

  const initiateWebRTCConnection = async (
    receiverId: string,
    transferId: string
  ) => {
    console.log("Initiating WebRTC connection to", receiverId);
    const webrtc = new WebRTCManager();
    webrtcConnections.current.set(receiverId, webrtc);

    // Set up connection state monitoring
    webrtc.onConnectionState((state) => {
      console.log(`Connection state with ${receiverId}:`, state);
      if (state === "connected") {
        setIsConnected(true);
      } else if (state === "failed" || state === "disconnected") {
        setIsConnected(false);
      }
    });

    // Set up error handling
    webrtc.onErrorOccurred((error) => {
      console.error(`WebRTC error with ${receiverId}:`, error);
      // Remove failed connection
      webrtcConnections.current.delete(receiverId);
      fileTransferManagers.current.delete(receiverId);
    });

    // Set up file transfer manager
    const fileTransfer = new FileTransferManager(webrtc);
    fileTransferManagers.current.set(receiverId, fileTransfer);

    fileTransfer.onProgress((progress) => {
      setActiveTransfers((prev) => {
        const index = prev.findIndex(
          (t) => t.transferId === progress.transferId
        );
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = progress;
          return updated;
        } else {
          return [...prev, progress];
        }
      });
    });

    // Set up WebRTC signaling
    webrtc.onSignaling(async (signalingMessage) => {
      if (signalingRef.current) {
        await signalingRef.current.sendMessage(receiverId, signalingMessage);
      }
    });

    try {
      // Create offer
      const offer = await webrtc.createOffer();

      if (signalingRef.current) {
        await signalingRef.current.sendMessage(receiverId, {
          type: "offer",
          data: offer,
        });
      }
    } catch (error) {
      console.error("Failed to create offer:", error);
      webrtcConnections.current.delete(receiverId);
      fileTransferManagers.current.delete(receiverId);
    }
  };

  const sendFile = async (file: File, targetDevice: Device) => {
    const transferId = uuidv4();

    try {
      // Send transfer request
      if (signalingRef.current) {
        await signalingRef.current.sendMessage(targetDevice.id, {
          type: "transfer-request",
          data: {
            transferId,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
          },
        });
      }

      // Store file for sending after WebRTC connection is established
      // The file will be sent when the transfer is accepted and WebRTC connection is ready
      const fileTransfer = fileTransferManagers.current.get(targetDevice.id);
      if (fileTransfer) {
        await fileTransfer.sendFile(file, transferId);
      } else {
        console.log(
          "File transfer manager not ready, will send after connection is established"
        );
        // Store the file and transferId for later use
        // This will be handled when the WebRTC connection is established
      }
    } catch (error) {
      console.error("Failed to send file:", error);
      throw error;
    }
  };

  const acceptTransfer = async (requestId: string) => {
    const request = transferRequests.find((r) => r.id === requestId);
    if (!request || !signalingRef.current) return;

    // Send acceptance response
    await signalingRef.current.sendMessage(request.senderId, {
      type: "transfer-response",
      data: {
        transferId: requestId,
        accepted: true,
      },
    });

    // Remove from requests
    setTransferRequests((prev) => prev.filter((r) => r.id !== requestId));
  };

  const rejectTransfer = async (requestId: string) => {
    const request = transferRequests.find((r) => r.id === requestId);
    if (!request || !signalingRef.current) return;

    // Send rejection response
    await signalingRef.current.sendMessage(request.senderId, {
      type: "transfer-response",
      data: {
        transferId: requestId,
        accepted: false,
      },
    });

    // Remove from requests
    setTransferRequests((prev) => prev.filter((r) => r.id !== requestId));
  };

  return {
    devices,
    transferRequests,
    activeTransfers,
    isConnected,
    deviceId: deviceIdRef.current,
    sendFile,
    acceptTransfer,
    rejectTransfer,
  };
}
