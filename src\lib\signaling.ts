import { Device, SignalingMessage } from '@/types';

export class SignalingClient {
  private deviceId: string;
  private deviceName: string;
  private baseUrl: string;
  private pollingInterval: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private onDevicesUpdate: ((devices: Device[]) => void) | null = null;
  private onMessage: ((message: SignalingMessage) => void) | null = null;
  private isPolling = false;

  constructor(deviceId: string, deviceName: string, baseUrl = '/api/signaling') {
    this.deviceId = deviceId;
    this.deviceName = deviceName;
    this.baseUrl = baseUrl;
  }

  async start(): Promise<void> {
    try {
      // Register device
      await this.register();
      
      // Start polling for messages
      this.startPolling();
      
      // Start heartbeat
      this.startHeartbeat();
      
      // Start device discovery
      this.startDeviceDiscovery();
      
      console.log('Signaling client started');
    } catch (error) {
      console.error('Failed to start signaling client:', error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isPolling = false;
    
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    try {
      // Unregister device
      await fetch(`${this.baseUrl}?deviceId=${this.deviceId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Failed to unregister device:', error);
    }

    console.log('Signaling client stopped');
  }

  private async register(): Promise<void> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'register',
        deviceId: this.deviceId,
        deviceName: this.deviceName,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to register device');
    }
  }

  private startPolling(): void {
    this.isPolling = true;
    this.poll();
  }

  private async poll(): Promise<void> {
    if (!this.isPolling) return;

    try {
      const response = await fetch(
        `${this.baseUrl}?deviceId=${this.deviceId}&action=poll`,
        {
          method: 'GET',
        }
      );

      if (response.ok) {
        const data = await response.json();
        
        if (data.messages && data.messages.length > 0) {
          data.messages.forEach((message: SignalingMessage) => {
            if (this.onMessage) {
              this.onMessage(message);
            }
          });
        }
      }
    } catch (error) {
      console.error('Polling error:', error);
    }

    // Continue polling with a short delay
    if (this.isPolling) {
      setTimeout(() => this.poll(), 1000);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(async () => {
      try {
        await fetch(this.baseUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'heartbeat',
            deviceId: this.deviceId,
          }),
        });
      } catch (error) {
        console.error('Heartbeat error:', error);
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  private startDeviceDiscovery(): void {
    const discoverDevices = async () => {
      try {
        const response = await fetch(
          `${this.baseUrl}?deviceId=${this.deviceId}&action=devices`,
          {
            method: 'GET',
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (this.onDevicesUpdate && data.devices) {
            this.onDevicesUpdate(data.devices);
          }
        }
      } catch (error) {
        console.error('Device discovery error:', error);
      }
    };

    // Discover devices immediately and then every 5 seconds
    discoverDevices();
    setInterval(discoverDevices, 5000);
  }

  async sendMessage(targetId: string, message: Omit<SignalingMessage, 'from' | 'timestamp'>): Promise<void> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send',
          deviceId: this.deviceId,
          targetId,
          message: {
            ...message,
            from: this.deviceId,
            timestamp: Date.now(),
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  // Event handlers
  onDevices(callback: (devices: Device[]) => void): void {
    this.onDevicesUpdate = callback;
  }

  onSignalingMessage(callback: (message: SignalingMessage) => void): void {
    this.onMessage = callback;
  }

  getDeviceId(): string {
    return this.deviceId;
  }

  getDeviceName(): string {
    return this.deviceName;
  }
}
