// DO NOT MANUALLY EDIT THESE TYPES
// You can regenerate this file by running `pnpm swc-build-wasm` in the root of the repo.

/* tslint:disable */
/* eslint-disable */
export function mdxCompileSync(value: string, opts: any): any
export function mdxCompile(value: string, opts: any): Promise<any>
export function minifySync(s: string, opts: any): any
export function minify(s: string, opts: any): Promise<any>
export function transformSync(s: any, opts: any): any
export function transform(s: any, opts: any): Promise<any>
export function parseSync(s: string, opts: any): any
export function parse(s: string, opts: any): Promise<any>
export function expandNextJsTemplate(
  content: Uint8Array,
  template_path: string,
  next_package_dir_path: string,
  replacements: any,
  injections: any,
  imports: any
): string
